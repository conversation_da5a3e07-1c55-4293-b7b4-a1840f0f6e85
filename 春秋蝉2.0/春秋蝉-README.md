# 春秋蝉 (<PERSON><PERSON><PERSON> Chan) 🦗

**专业的Burp Suite参数安全检测扩展插件**

一个功能强大的Burp Suite插件，专门用于检测Web应用程序中的参数安全漏洞和敏感信息泄露。

## ✨ 核心功能

### 🔍 参数检测与分析
- **智能参数提取**: 自动识别GET、POST、Cookie、Header等各种参数
- **多格式支持**: 支持表单数据、JSON、XML、多部分表单等
- **参数污染检测**: 识别同名参数的重复提交问题

### 💉 SQL注入检测 (15+种模式)
- **多种注入模式**: Union、Boolean、Time-based、Error-based等
- **数据库指纹识别**: MySQL、PostgreSQL、Oracle、SQL Server、SQLite
- **智能载荷优化**: 根据应用特征优化注入载荷
- **误报减少**: 多重验证机制减少误报率

### 🔧 API模糊测试
- **命令注入检测**: 测试系统命令执行漏洞
- **路径遍历检测**: 识别目录遍历和文件包含漏洞
- **XSS检测**: 检测反射型跨站脚本攻击
- **多平台支持**: Linux、Windows等不同操作系统环境

### 🔐 敏感信息检测 (NEW!)
- **21个内置规则**: 
  - API密钥 (AWS、Google、GitHub、Slack等)
  - 数据库连接字符串 (MySQL、PostgreSQL、MongoDB)
  - 私钥和证书 (RSA、SSH)
  - 个人敏感信息 (邮箱、电话、信用卡)
  - 代码中的硬编码密码
- **自定义规则**: 支持添加自定义正则表达式规则
- **持久化存储**: 自定义规则自动保存，支持跨设备同步
- **JS文件专项**: 特别针对JavaScript文件进行深度检测

### 🛡️ 域名白名单 (25+个预设)
- **智能过滤**: 自动跳过以下系统服务域名：
  - Google服务 (googleapis.com、gvt1.com等)
  - Microsoft更新 (update.microsoft.com等)
  - Mozilla下载 (download.mozilla.org等)
  - 各种CDN服务
- **性能优化**: 减少90%以上的误扫，提升扫描效率
- **自定义管理**: 支持添加/删除自定义白名单域名

### 🕷️ 同域爬虫
- **受限制爬取**: 仅爬取同域名下的页面，避免越界
- **深度控制**: 可配置爬取深度，默认最大深度为2层
- **智能过滤**: 自动过滤静态资源和危险操作链接
- **URL数量限制**: 防止无限爬取，保护目标服务器

### 📊 漏洞管理
- **实时监控**: 实时显示发现的漏洞信息
- **详细报告**: 提供漏洞详情、载荷、证据和修复建议
- **HTML导出**: 支持导出专业的HTML格式安全报告
- **统计分析**: 按严重程度统计漏洞分布

## 🚀 安装方法

### 1. 下载插件
```bash
# 下载编译好的插件文件
chunqiu-chan-1.0.0.jar
```

### 2. 安装到Burp Suite
1. 打开Burp Suite
2. 进入 `Extensions` -> `Installed`
3. 点击 `Add` 按钮
4. 选择 `Java` 类型
5. 选择下载的 `chunqiu-chan-1.0.0.jar` 文件
6. 点击 `Next` 完成安装

### 3. 验证安装
- 安装成功后，在Burp Suite中会出现 **"春秋蝉"** 标签页
- 控制台会显示：`春秋蝉 (ChunQiu Chan) 插件加载成功！`

## 🎯 使用方法

### 基础配置
1. **启用功能**: 在春秋蝉标签页中勾选需要的功能
   - ✅ SQL注入检测
   - ✅ API模糊测试
   - ✅ 敏感信息检测
   - ⚪ 同域爬虫 (可选)

2. **域名白名单**: 点击"域名白名单"按钮管理过滤规则

3. **敏感信息规则**: 点击"敏感信息规则"按钮管理检测规则

### 自定义敏感信息规则
1. 点击"敏感信息规则"按钮
2. 在底部面板填写：
   - **规则名称**: 如 "内部API密钥"
   - **正则表达式**: 如 `INTERNAL_KEY_[A-Z0-9]{16}`
   - **描述**: 如 "检测内部系统API密钥"
   - **严重程度**: 选择 低危/中危/高危/严重
3. 点击"添加规则"保存

### 跨设备同步规则
1. 在电脑A上添加自定义规则
2. 复制两个文件到电脑B：
   - `chunqiu-chan-1.0.0.jar` (插件文件)
   - `sensitive_rules.properties` (配置文件)
3. 在电脑B上安装插件，自定义规则会自动加载

## 📈 检测效果

### SQL注入检测
```
✅ 发现SQL注入漏洞: http://example.com/api?id=1
   参数: id
   载荷: 1' OR '1'='1
   数据库: MySQL
   严重程度: 高危
```

### 敏感信息检测
```
✅ 发现敏感信息: https://example.com/app.js
   类型: Google API密钥
   内容: AIzaSyDaGmWKa4JsXZ-HjGw1234567890123456
   严重程度: 高危
```

### 域名白名单效果
```
跳过白名单域名: edgedl.me.gvt1.com
跳过白名单域名: update.googleapis.com
正在分析请求: GET http://your-target-site.com/api?id=123
```

## 🔧 技术特性

- **高性能**: 异步处理，不影响正常浏览
- **低误报**: 多重验证机制
- **可扩展**: 支持自定义规则和白名单
- **持久化**: 配置自动保存
- **跨平台**: 支持Windows、macOS、Linux

## 📝 更新日志

### v1.0.0 (2025-08-03)
- ✨ 首次发布
- ✅ SQL注入检测 (15+种模式)
- ✅ API模糊测试
- ✅ 敏感信息检测 (21个内置规则)
- ✅ 自定义规则持久化
- ✅ 域名白名单 (25+个预设)
- ✅ 同域爬虫
- ✅ 漏洞管理和导出

## 🤝 贡献

欢迎提交Issue和Pull Request来改进春秋蝉！

## 📄 许可证

本项目采用 MIT 许可证。

---

**春秋蝉 (ChunQiu Chan)** - 让Web安全检测更加智能和高效 🦗
