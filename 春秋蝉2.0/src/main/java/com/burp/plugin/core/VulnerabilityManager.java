package com.burp.plugin.core;

import com.burp.plugin.model.Vulnerability;
import com.burp.plugin.model.VulnerabilityType;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 漏洞管理器 - 负责存储、管理和分析发现的漏洞
 */
public class VulnerabilityManager {
    
    private final List<Vulnerability> vulnerabilities;
    private final Map<String, Set<String>> testedUrls; // URL -> 已测试的参数集合
    private final List<VulnerabilityListener> listeners;
    
    public VulnerabilityManager() {
        this.vulnerabilities = new CopyOnWriteArrayList<>();
        this.testedUrls = new ConcurrentHashMap<>();
        this.listeners = new CopyOnWriteArrayList<>();
    }
    
    /**
     * 添加新发现的漏洞
     */
    public void addVulnerability(Vulnerability vulnerability) {
        // 检查是否已存在相同的漏洞
        if (!isDuplicate(vulnerability)) {
            vulnerabilities.add(vulnerability);
            notifyListeners(vulnerability);
        }
    }
    
    /**
     * 检查是否为重复漏洞
     */
    private boolean isDuplicate(Vulnerability newVuln) {
        for (Vulnerability existing : vulnerabilities) {
            if (existing.getUrl().equals(newVuln.getUrl()) &&
                existing.getParameter().equals(newVuln.getParameter()) &&
                existing.getType() == newVuln.getType()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 标记URL和参数已被测试
     */
    public void markAsTested(String url, String parameter) {
        // 使用URL的基础部分（去除查询参数）作为key，提高缓存效率
        String baseUrl = getBaseUrl(url);
        if (!testedUrls.containsKey(baseUrl)) {
            testedUrls.put(baseUrl, ConcurrentHashMap.newKeySet());
        }
        testedUrls.get(baseUrl).add(parameter);
    }

    /**
     * 检查URL和参数是否已被测试
     */
    public boolean isAlreadyTested(String url, String parameter) {
        String baseUrl = getBaseUrl(url);
        Set<String> testedParams = testedUrls.get(baseUrl);
        return testedParams != null && testedParams.contains(parameter);
    }

    /**
     * 获取URL的基础部分（去除查询参数和fragment）
     */
    private String getBaseUrl(String url) {
        try {
            int queryIndex = url.indexOf('?');
            int fragmentIndex = url.indexOf('#');

            int endIndex = url.length();
            if (queryIndex != -1) {
                endIndex = Math.min(endIndex, queryIndex);
            }
            if (fragmentIndex != -1) {
                endIndex = Math.min(endIndex, fragmentIndex);
            }

            return url.substring(0, endIndex);
        } catch (Exception e) {
            return url; // 如果解析失败，返回原URL
        }
    }
    
    /**
     * 获取所有漏洞
     */
    public List<Vulnerability> getAllVulnerabilities() {
        return new ArrayList<>(vulnerabilities);
    }
    
    /**
     * 根据类型获取漏洞
     */
    public List<Vulnerability> getVulnerabilitiesByType(VulnerabilityType type) {
        List<Vulnerability> result = new ArrayList<>();
        for (Vulnerability v : vulnerabilities) {
            if (v.getType() == type) {
                result.add(v);
            }
        }
        return result;
    }
    
    /**
     * 获取漏洞统计信息
     */
    public Map<VulnerabilityType, Integer> getVulnerabilityStats() {
        Map<VulnerabilityType, Integer> stats = new HashMap<>();
        for (VulnerabilityType type : VulnerabilityType.values()) {
            stats.put(type, 0);
        }
        
        for (Vulnerability vuln : vulnerabilities) {
            stats.merge(vuln.getType(), 1, Integer::sum);
        }
        
        return stats;
    }
    
    /**
     * 清除所有漏洞记录
     */
    public void clearAll() {
        vulnerabilities.clear();
        testedUrls.clear();
        notifyListenersCleared();
    }
    
    /**
     * 清除特定类型的漏洞
     */
    public void clearByType(VulnerabilityType type) {
        vulnerabilities.removeIf(v -> v.getType() == type);
        notifyListenersCleared();
    }
    
    /**
     * 添加漏洞监听器
     */
    public void addVulnerabilityListener(VulnerabilityListener listener) {
        listeners.add(listener);
    }
    
    /**
     * 移除漏洞监听器
     */
    public void removeVulnerabilityListener(VulnerabilityListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * 通知监听器新漏洞
     */
    private void notifyListeners(Vulnerability vulnerability) {
        for (VulnerabilityListener listener : listeners) {
            try {
                listener.onVulnerabilityFound(vulnerability);
            } catch (Exception e) {
                // 忽略监听器异常，避免影响主流程
            }
        }
    }
    
    /**
     * 通知监听器清除事件
     */
    private void notifyListenersCleared() {
        for (VulnerabilityListener listener : listeners) {
            try {
                listener.onVulnerabilitiesCleared();
            } catch (Exception e) {
                // 忽略监听器异常
            }
        }
    }
    
    /**
     * 漏洞监听器接口
     */
    public interface VulnerabilityListener {
        void onVulnerabilityFound(Vulnerability vulnerability);
        void onVulnerabilitiesCleared();
    }
    
    /**
     * 获取总漏洞数量
     */
    public int getTotalCount() {
        return vulnerabilities.size();
    }
    
    /**
     * 获取高危漏洞数量
     */
    public int getHighRiskCount() {
        int count = 0;
        for (Vulnerability v : vulnerabilities) {
            if (v.getSeverity() == Vulnerability.Severity.HIGH) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * 获取中危漏洞数量
     */
    public int getMediumRiskCount() {
        int count = 0;
        for (Vulnerability v : vulnerabilities) {
            if (v.getSeverity() == Vulnerability.Severity.MEDIUM) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * 获取低危漏洞数量
     */
    public int getLowRiskCount() {
        int count = 0;
        for (Vulnerability v : vulnerabilities) {
            if (v.getSeverity() == Vulnerability.Severity.LOW) {
                count++;
            }
        }
        return count;
    }
}
