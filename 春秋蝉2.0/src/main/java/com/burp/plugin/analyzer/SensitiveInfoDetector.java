package com.burp.plugin.analyzer;

import burp.*;
import com.burp.plugin.core.VulnerabilityManager;
import com.burp.plugin.core.CustomRulePersistence;
import com.burp.plugin.model.Vulnerability;
import com.burp.plugin.model.VulnerabilityType;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.File;
import java.io.FileInputStream;

/**
 * 敏感信息检测器 - 检测JS文件和响应中的敏感信息泄露
 */
public class SensitiveInfoDetector {
    
    private final IBurpExtenderCallbacks callbacks;
    private final IExtensionHelpers helpers;
    private final VulnerabilityManager vulnerabilityManager;
    private final CustomRulePersistence persistence;

    // 内置敏感信息正则表达式
    private final List<SensitivePattern> builtinPatterns;

    // 用户自定义正则表达式
    private final List<SensitivePattern> customPatterns;
    
    public SensitiveInfoDetector(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers,
                                VulnerabilityManager vulnerabilityManager) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.vulnerabilityManager = vulnerabilityManager;
        this.persistence = new CustomRulePersistence(callbacks);
        this.builtinPatterns = new ArrayList<>();
        this.customPatterns = new ArrayList<>();

        initializeBuiltinPatterns();
        loadConfigFilePatterns();
        loadCustomPatterns();
    }
    
    /**
     * 初始化内置敏感信息正则表达式
     * 基于GitHub上优秀的敏感信息检测规则
     */
    private void initializeBuiltinPatterns() {
        // API Keys
        addBuiltinPattern("AWS Access Key", 
            "AKIA[0-9A-Z]{16}", 
            "AWS访问密钥", Vulnerability.Severity.HIGH);
        
        addBuiltinPattern("AWS Secret Key", 
            "aws(.{0,20})?['\"][0-9a-zA-Z/+]{40}['\"]", 
            "AWS秘密密钥", Vulnerability.Severity.CRITICAL);
        
        addBuiltinPattern("Google API Key", 
            "AIza[0-9A-Za-z\\-_]{35}", 
            "Google API密钥", Vulnerability.Severity.HIGH);
        
        addBuiltinPattern("GitHub Token", 
            "ghp_[0-9a-zA-Z]{36}|github_pat_[0-9a-zA-Z_]{82}", 
            "GitHub访问令牌", Vulnerability.Severity.HIGH);
        
        addBuiltinPattern("Slack Token", 
            "xox[baprs]-[0-9a-zA-Z]{10,48}", 
            "Slack令牌", Vulnerability.Severity.HIGH);
        
        addBuiltinPattern("Facebook Access Token", 
            "EAACEdEose0cBA[0-9A-Za-z]+", 
            "Facebook访问令牌", Vulnerability.Severity.HIGH);
        
        addBuiltinPattern("Twitter API Key", 
            "[tT][wW][iI][tT][tT][eE][rR](.{0,20})?['\"][0-9a-zA-Z]{35,44}['\"]", 
            "Twitter API密钥", Vulnerability.Severity.HIGH);
        
        // Database Connections
        addBuiltinPattern("MySQL Connection", 
            "mysql://[a-zA-Z0-9._%+-]+:[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+:[0-9]+/[a-zA-Z0-9_]+", 
            "MySQL数据库连接字符串", Vulnerability.Severity.CRITICAL);
        
        addBuiltinPattern("PostgreSQL Connection", 
            "postgres://[a-zA-Z0-9._%+-]+:[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+:[0-9]+/[a-zA-Z0-9_]+", 
            "PostgreSQL数据库连接字符串", Vulnerability.Severity.CRITICAL);
        
        addBuiltinPattern("MongoDB Connection", 
            "mongodb://[a-zA-Z0-9._%+-]+:[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+:[0-9]+/[a-zA-Z0-9_]+", 
            "MongoDB数据库连接字符串", Vulnerability.Severity.CRITICAL);
        
        // JWT Tokens
        addBuiltinPattern("JWT Token", 
            "eyJ[A-Za-z0-9_/+-]*\\.eyJ[A-Za-z0-9_/+-]*\\.[A-Za-z0-9._/+-]*", 
            "JWT令牌", Vulnerability.Severity.MEDIUM);
        
        // Private Keys
        addBuiltinPattern("RSA Private Key", 
            "-----BEGIN RSA PRIVATE KEY-----[\\s\\S]*?-----END RSA PRIVATE KEY-----", 
            "RSA私钥", Vulnerability.Severity.CRITICAL);
        
        addBuiltinPattern("SSH Private Key", 
            "-----BEGIN OPENSSH PRIVATE KEY-----[\\s\\S]*?-----END OPENSSH PRIVATE KEY-----", 
            "SSH私钥", Vulnerability.Severity.CRITICAL);
        
        // Email and Phone
        addBuiltinPattern("Email Address", 
            "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}", 
            "邮箱地址", Vulnerability.Severity.LOW);
        
        addBuiltinPattern("Phone Number", 
            "(?:\\+86|86)?\\s*1[3-9]\\d{9}|\\+?1[2-9]\\d{2}[2-9]\\d{2}\\d{4}", 
            "电话号码", Vulnerability.Severity.LOW);
        
        // Credit Card
        addBuiltinPattern("Credit Card", 
            "(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|3[0-9]{13}|6(?:011|5[0-9]{2})[0-9]{12})", 
            "信用卡号", Vulnerability.Severity.HIGH);
        
        // IP Addresses
        addBuiltinPattern("Internal IP", 
            "(?:10\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|172\\.(?:1[6-9]|2[0-9]|3[0-1])\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|192\\.168\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))", 
            "内网IP地址", Vulnerability.Severity.MEDIUM);
        
        // Passwords in Code
        addBuiltinPattern("Password in Code", 
            "(?i)(password|pwd|pass)\\s*[=:]\\s*['\"][^'\"\\s]{6,}['\"]", 
            "代码中的密码", Vulnerability.Severity.HIGH);
        
        addBuiltinPattern("API Key in Code", 
            "(?i)(api[_-]?key|apikey|secret[_-]?key)\\s*[=:]\\s*['\"][^'\"\\s]{10,}['\"]", 
            "代码中的API密钥", Vulnerability.Severity.HIGH);
        
        // URLs with credentials
        addBuiltinPattern("URL with Credentials", 
            "https?://[a-zA-Z0-9._%+-]+:[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+", 
            "包含凭据的URL", Vulnerability.Severity.HIGH);
        
        // Base64 encoded secrets (common patterns)
        addBuiltinPattern("Base64 Secret", 
            "(?i)(secret|key|token|password)\\s*[=:]\\s*['\"][A-Za-z0-9+/]{20,}={0,2}['\"]", 
            "Base64编码的秘密信息", Vulnerability.Severity.MEDIUM);
    }
    
    /**
     * 添加内置模式
     */
    private void addBuiltinPattern(String name, String regex, String description, Vulnerability.Severity severity) {
        try {
            Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE | Pattern.MULTILINE);
            builtinPatterns.add(new SensitivePattern(name, pattern, description, severity, true));
        } catch (Exception e) {
            callbacks.printError("Failed to compile regex for " + name + ": " + e.getMessage());
        }
    }
    
    /**
     * 检测响应中的敏感信息
     */
    public void detectSensitiveInfo(IHttpRequestResponse messageInfo) {
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(messageInfo);
            IResponseInfo responseInfo = helpers.analyzeResponse(messageInfo.getResponse());
            
            String url = requestInfo.getUrl().toString();
            String response = helpers.bytesToString(messageInfo.getResponse());
            
            // 检查是否为JS文件或包含JS内容
            boolean isJsFile = isJavaScriptContent(url, responseInfo);
            
            if (isJsFile) {
                callbacks.printOutput("检测JS文件中的敏感信息: " + url);
            }
            
            // 检测所有模式
            detectWithPatterns(builtinPatterns, messageInfo, response, isJsFile);
            detectWithPatterns(customPatterns, messageInfo, response, isJsFile);
            
        } catch (Exception e) {
            callbacks.printError("Error detecting sensitive info: " + e.getMessage());
        }
    }
    
    /**
     * 使用模式列表检测敏感信息
     */
    private void detectWithPatterns(List<SensitivePattern> patterns, IHttpRequestResponse messageInfo, 
                                   String response, boolean isJsFile) {
        IRequestInfo requestInfo = helpers.analyzeRequest(messageInfo);
        String url = requestInfo.getUrl().toString();
        
        for (SensitivePattern sensitivePattern : patterns) {
            Matcher matcher = sensitivePattern.pattern.matcher(response);
            
            while (matcher.find()) {
                String matchedText = matcher.group();
                
                // 避免过长的匹配文本
                if (matchedText.length() > 200) {
                    matchedText = matchedText.substring(0, 200) + "...";
                }
                
                // 创建漏洞记录
                String evidence = "发现敏感信息: " + sensitivePattern.description + 
                                "\n匹配内容: " + matchedText +
                                (isJsFile ? "\n文件类型: JavaScript" : "");
                
                Vulnerability vulnerability = new Vulnerability.Builder()
                        .type(VulnerabilityType.SENSITIVE_DATA_EXPOSURE)
                        .url(url)
                        .parameter("response_content")
                        .payload(sensitivePattern.name)
                        .evidence(evidence)
                        .severity(sensitivePattern.severity)
                        .method(requestInfo.getMethod())
                        .originalRequest(helpers.bytesToString(messageInfo.getRequest()))
                        .response(response.length() > 2000 ? response.substring(0, 2000) + "..." : response)
                        .build();
                
                vulnerabilityManager.addVulnerability(vulnerability);
                
                callbacks.printOutput("发现敏感信息: " + sensitivePattern.name + " 在 " + url);
            }
        }
    }
    
    /**
     * 判断是否为JavaScript内容
     */
    private boolean isJavaScriptContent(String url, IResponseInfo responseInfo) {
        // 检查URL扩展名
        if (url.toLowerCase().endsWith(".js")) {
            return true;
        }
        
        // 检查Content-Type
        List<String> headers = responseInfo.getHeaders();
        for (String header : headers) {
            if (header.toLowerCase().startsWith("content-type:")) {
                String contentType = header.toLowerCase();
                if (contentType.contains("javascript") || 
                    contentType.contains("application/js") ||
                    contentType.contains("text/js")) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 加载config.txt文件中的规则
     */
    private void loadConfigFilePatterns() {
        try {
            // 首先尝试从JAR文件同目录加载config.txt
            String jarPath = getJarPath();
            File configFile = null;

            if (jarPath != null) {
                File jarDir = new File(jarPath).getParentFile();
                configFile = new File(jarDir, "config.txt");
            }

            InputStream inputStream = null;

            if (configFile != null && configFile.exists()) {
                // 从外部文件加载
                inputStream = new FileInputStream(configFile);
                callbacks.printOutput("从外部文件加载敏感信息规则: " + configFile.getAbsolutePath());
            } else {
                // 从JAR内部资源加载
                inputStream = this.getClass().getClassLoader().getResourceAsStream("config.txt");
                if (inputStream != null) {
                    callbacks.printOutput("从JAR内部资源加载敏感信息规则");
                }
            }

            if (inputStream != null) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
                    String line;
                    int lineNumber = 0;
                    int loadedCount = 0;

                    while ((line = reader.readLine()) != null) {
                        lineNumber++;
                        line = line.trim();

                        // 跳过空行和注释
                        if (line.isEmpty() || line.startsWith("#")) {
                            continue;
                        }

                        // 解析格式: 规则名称:正则表达式
                        int colonIndex = line.indexOf(':');
                        if (colonIndex > 0 && colonIndex < line.length() - 1) {
                            String name = line.substring(0, colonIndex).trim();
                            String regex = line.substring(colonIndex + 1).trim();

                            try {
                                Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE | Pattern.MULTILINE);
                                builtinPatterns.add(new SensitivePattern(name, pattern, name, Vulnerability.Severity.HIGH, true));
                                loadedCount++;
                            } catch (Exception e) {
                                callbacks.printError("配置文件第" + lineNumber + "行正则表达式错误: " + name + " - " + e.getMessage());
                            }
                        } else {
                            callbacks.printError("配置文件第" + lineNumber + "行格式错误，应为 '规则名称:正则表达式'");
                        }
                    }

                    callbacks.printOutput("从配置文件加载了 " + loadedCount + " 个敏感信息检测规则");
                }
            } else {
                callbacks.printOutput("未找到config.txt配置文件，使用默认内置规则");
            }

        } catch (Exception e) {
            callbacks.printError("加载配置文件时出错: " + e.getMessage());
        }
    }

    /**
     * 获取JAR文件路径
     */
    private String getJarPath() {
        try {
            String className = this.getClass().getName().replace('.', '/') + ".class";
            String classJar = this.getClass().getClassLoader().getResource(className).toString();

            if (classJar.startsWith("jar:")) {
                String jarPath = classJar.substring(4, classJar.indexOf("!/"));
                if (jarPath.startsWith("file:")) {
                    return jarPath.substring(5);
                }
                return jarPath;
            }
        } catch (Exception e) {
            // 忽略错误，返回null
        }
        return null;
    }

    /**
     * 加载自定义规则
     */
    private void loadCustomPatterns() {
        try {
            List<CustomRulePersistence.CustomRule> savedRules = persistence.loadCustomRules();
            for (CustomRulePersistence.CustomRule rule : savedRules) {
                try {
                    Pattern pattern = Pattern.compile(rule.regex, Pattern.CASE_INSENSITIVE | Pattern.MULTILINE);
                    customPatterns.add(new SensitivePattern(rule.name, pattern, rule.description, rule.severity, false));
                } catch (Exception e) {
                    callbacks.printError("加载自定义规则失败: " + rule.name + " - " + e.getMessage());
                }
            }
            if (!savedRules.isEmpty()) {
                callbacks.printOutput("已加载 " + savedRules.size() + " 个自定义敏感信息规则");
            }
        } catch (Exception e) {
            callbacks.printError("加载自定义规则时出错: " + e.getMessage());
        }
    }

    /**
     * 保存自定义规则
     */
    private void saveCustomPatterns() {
        try {
            List<CustomRulePersistence.CustomRule> rulesToSave = new ArrayList<>();
            for (SensitivePattern pattern : customPatterns) {
                if (!pattern.isBuiltin) {
                    rulesToSave.add(new CustomRulePersistence.CustomRule(
                        pattern.name,
                        pattern.pattern.pattern(),
                        pattern.description,
                        pattern.severity
                    ));
                }
            }
            persistence.saveCustomRules(rulesToSave);
        } catch (Exception e) {
            callbacks.printError("保存自定义规则时出错: " + e.getMessage());
        }
    }

    /**
     * 添加自定义正则表达式
     */
    public void addCustomPattern(String name, String regex, String description, Vulnerability.Severity severity) {
        try {
            Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE | Pattern.MULTILINE);
            customPatterns.add(new SensitivePattern(name, pattern, description, severity, false));
            callbacks.printOutput("已添加自定义敏感信息规则: " + name);

            // 立即保存到文件
            saveCustomPatterns();
        } catch (Exception e) {
            callbacks.printError("Failed to add custom pattern " + name + ": " + e.getMessage());
        }
    }
    
    /**
     * 移除自定义正则表达式
     */
    public void removeCustomPattern(String name) {
        customPatterns.removeIf(pattern -> pattern.name.equals(name) && !pattern.isBuiltin);
        callbacks.printOutput("已移除自定义敏感信息规则: " + name);

        // 立即保存到文件
        saveCustomPatterns();
    }
    
    /**
     * 获取所有模式
     */
    public List<SensitivePattern> getAllPatterns() {
        List<SensitivePattern> allPatterns = new ArrayList<>();
        allPatterns.addAll(builtinPatterns);
        allPatterns.addAll(customPatterns);
        return allPatterns;
    }
    
    /**
     * 获取自定义模式
     */
    public List<SensitivePattern> getCustomPatterns() {
        return new ArrayList<>(customPatterns);
    }
    
    /**
     * 清空自定义模式
     */
    public void clearCustomPatterns() {
        customPatterns.removeIf(pattern -> !pattern.isBuiltin);
        callbacks.printOutput("已清空所有自定义敏感信息规则");

        // 立即保存到文件
        saveCustomPatterns();
    }

    /**
     * 获取配置文件路径
     */
    public String getConfigFilePath() {
        return persistence.getConfigFilePath();
    }
    
    /**
     * 敏感信息模式类
     */
    public static class SensitivePattern {
        public final String name;
        public final Pattern pattern;
        public final String description;
        public final Vulnerability.Severity severity;
        public final boolean isBuiltin;
        
        public SensitivePattern(String name, Pattern pattern, String description, 
                               Vulnerability.Severity severity, boolean isBuiltin) {
            this.name = name;
            this.pattern = pattern;
            this.description = description;
            this.severity = severity;
            this.isBuiltin = isBuiltin;
        }
    }
}
