package com.burp.plugin.ui;

import com.burp.plugin.<PERSON><PERSON><PERSON><PERSON><PERSON>;
import com.burp.plugin.core.VulnerabilityManager;
import com.burp.plugin.model.Vulnerability;
import com.burp.plugin.model.VulnerabilityType;
import com.burp.plugin.analyzer.SensitiveInfoDetector;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableRowSorter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 插件主界面
 */
public class MainPanel extends JPanel implements VulnerabilityManager.VulnerabilityListener {
    
    private final ChunQiu<PERSON>han plugin;
    private final VulnerabilityManager vulnerabilityManager;
    
    // UI组件
    private JTable vulnerabilityTable;
    private DefaultTableModel tableModel;
    private JLabel statsLabel;
    private JTextArea detailsArea;
    private JCheckBox enabledCheckBox;
    private JCheckBox sqlInjectionCheckBox;
    private JCheckBox apiFuzzingCheckBox;
    private JCheckBox crawlerCheckBox;
    private JCheckBox sensitiveInfoCheckBox;
    
    // 统计标签
    private JLabel totalLabel;
    private JLabel highLabel;
    private JLabel mediumLabel;
    private JLabel lowLabel;
    
    public MainPanel(ChunQiuChan plugin, VulnerabilityManager vulnerabilityManager) {
        this.plugin = plugin;
        this.vulnerabilityManager = vulnerabilityManager;
        
        // 注册为漏洞监听器
        vulnerabilityManager.addVulnerabilityListener(this);
        
        initializeUI();
        updateStats();
    }
    
    /**
     * 初始化UI界面
     */
    private void initializeUI() {
        setLayout(new BorderLayout());
        
        // 创建顶部控制面板
        JPanel controlPanel = createControlPanel();
        add(controlPanel, BorderLayout.NORTH);
        
        // 创建中间的分割面板
        JSplitPane splitPane = createSplitPane();
        add(splitPane, BorderLayout.CENTER);
        
        // 创建底部状态面板
        JPanel statusPanel = createStatusPanel();
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建控制面板
     */
    private JPanel createControlPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        panel.setBorder(BorderFactory.createTitledBorder("插件控制"));

        // 插件开关
        enabledCheckBox = new JCheckBox("启用插件", plugin.isEnabled());
        enabledCheckBox.addActionListener(e -> plugin.setEnabled(enabledCheckBox.isSelected()));
        panel.add(enabledCheckBox);

        panel.add(new JSeparator(SwingConstants.VERTICAL));

        // 功能开关
        sqlInjectionCheckBox = new JCheckBox("SQL注入检测", plugin.isSqlInjectionTestEnabled());
        sqlInjectionCheckBox.addActionListener(e -> plugin.setSqlInjectionTestEnabled(sqlInjectionCheckBox.isSelected()));
        panel.add(sqlInjectionCheckBox);

        apiFuzzingCheckBox = new JCheckBox("API模糊测试", plugin.isApiFuzzingEnabled());
        apiFuzzingCheckBox.addActionListener(e -> plugin.setApiFuzzingEnabled(apiFuzzingCheckBox.isSelected()));
        panel.add(apiFuzzingCheckBox);

        crawlerCheckBox = new JCheckBox("同域爬虫", plugin.isCrawlerEnabled());
        crawlerCheckBox.addActionListener(e -> plugin.setCrawlerEnabled(crawlerCheckBox.isSelected()));
        panel.add(crawlerCheckBox);

        sensitiveInfoCheckBox = new JCheckBox("敏感信息检测", plugin.isSensitiveInfoDetectionEnabled());
        sensitiveInfoCheckBox.addActionListener(e -> plugin.setSensitiveInfoDetectionEnabled(sensitiveInfoCheckBox.isSelected()));
        panel.add(sensitiveInfoCheckBox);

        panel.add(new JSeparator(SwingConstants.VERTICAL));

        // 操作按钮
        JButton clearButton = new JButton("清空记录");
        clearButton.addActionListener(e -> {
            int result = JOptionPane.showConfirmDialog(this,
                "确定要清空所有漏洞记录吗？",
                "确认清空", JOptionPane.YES_NO_OPTION);
            if (result == JOptionPane.YES_OPTION) {
                vulnerabilityManager.clearAll();
            }
        });
        panel.add(clearButton);

        JButton exportButton = new JButton("导出报告");
        exportButton.addActionListener(e -> exportReport());
        panel.add(exportButton);

        panel.add(new JSeparator(SwingConstants.VERTICAL));

        JButton whitelistButton = new JButton("域名白名单");
        whitelistButton.addActionListener(e -> showDomainWhitelistDialog());
        panel.add(whitelistButton);

        JButton sensitiveRulesButton = new JButton("敏感信息规则");
        sensitiveRulesButton.addActionListener(e -> showSensitiveRulesDialog());
        panel.add(sensitiveRulesButton);

        return panel;
    }
    
    /**
     * 创建分割面板
     */
    private JSplitPane createSplitPane() {
        // 创建漏洞表格
        JPanel tablePanel = createTablePanel();
        
        // 创建详情面板
        JPanel detailsPanel = createDetailsPanel();
        
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT, tablePanel, detailsPanel);
        splitPane.setDividerLocation(300);
        splitPane.setResizeWeight(0.7);
        
        return splitPane;
    }
    
    /**
     * 创建表格面板
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("发现的安全漏洞"));

        // 创建表格模型
        String[] columnNames = {"漏洞类型", "严重程度", "URL地址", "参数名称", "发现时间"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        vulnerabilityTable = new JTable(tableModel);
        vulnerabilityTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        vulnerabilityTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                showVulnerabilityDetails();
            }
        });
        
        // 设置表格排序
        TableRowSorter<DefaultTableModel> sorter = new TableRowSorter<>(tableModel);
        vulnerabilityTable.setRowSorter(sorter);
        
        // 设置列宽
        vulnerabilityTable.getColumnModel().getColumn(0).setPreferredWidth(120);
        vulnerabilityTable.getColumnModel().getColumn(1).setPreferredWidth(80);
        vulnerabilityTable.getColumnModel().getColumn(2).setPreferredWidth(300);
        vulnerabilityTable.getColumnModel().getColumn(3).setPreferredWidth(100);
        vulnerabilityTable.getColumnModel().getColumn(4).setPreferredWidth(120);
        
        JScrollPane scrollPane = new JScrollPane(vulnerabilityTable);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建详情面板
     */
    private JPanel createDetailsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("漏洞详细信息"));

        detailsArea = new JTextArea();
        detailsArea.setEditable(false);
        detailsArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        detailsArea.setText("请选择一个漏洞查看详细信息...");

        JScrollPane scrollPane = new JScrollPane(detailsArea);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }
    
    /**
     * 创建状态面板
     */
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        panel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        totalLabel = new JLabel("总计: 0");
        highLabel = new JLabel("高危: 0");
        mediumLabel = new JLabel("中危: 0");
        lowLabel = new JLabel("低危: 0");
        
        panel.add(totalLabel);
        panel.add(new JSeparator(SwingConstants.VERTICAL));
        panel.add(highLabel);
        panel.add(new JSeparator(SwingConstants.VERTICAL));
        panel.add(mediumLabel);
        panel.add(new JSeparator(SwingConstants.VERTICAL));
        panel.add(lowLabel);
        
        return panel;
    }
    
    /**
     * 显示漏洞详情
     */
    private void showVulnerabilityDetails() {
        int selectedRow = vulnerabilityTable.getSelectedRow();
        if (selectedRow == -1) {
            detailsArea.setText("请选择一个漏洞查看详细信息...");
            return;
        }
        
        // 获取实际行索引（考虑排序）
        int modelRow = vulnerabilityTable.convertRowIndexToModel(selectedRow);
        List<Vulnerability> vulnerabilities = vulnerabilityManager.getAllVulnerabilities();
        
        if (modelRow < vulnerabilities.size()) {
            Vulnerability vuln = vulnerabilities.get(modelRow);
            displayVulnerabilityDetails(vuln);
        }
    }
    
    /**
     * 显示漏洞详细信息
     */
    private void displayVulnerabilityDetails(Vulnerability vuln) {
        StringBuilder details = new StringBuilder();
        details.append("=== 漏洞详细信息 ===\n\n");
        details.append("漏洞ID: ").append(vuln.getId()).append("\n");
        details.append("漏洞类型: ").append(vuln.getType().getDisplayName()).append("\n");
        details.append("严重程度: ").append(getSeverityInChinese(vuln.getSeverity())).append("\n");
        details.append("URL地址: ").append(vuln.getUrl()).append("\n");
        details.append("参数名称: ").append(vuln.getParameter()).append("\n");
        details.append("请求方法: ").append(vuln.getMethod()).append("\n");
        details.append("发现时间: ").append(vuln.getFormattedDiscoveryTime()).append("\n\n");

        if (vuln.getPayload() != null) {
            details.append("测试载荷: ").append(vuln.getPayload()).append("\n\n");
        }

        details.append("漏洞描述:\n").append(getDescriptionInChinese(vuln)).append("\n\n");
        details.append("漏洞证据:\n").append(vuln.getEvidence()).append("\n\n");
        details.append("修复建议:\n").append(getRecommendationInChinese(vuln)).append("\n\n");

        if (vuln.getOriginalRequest() != null) {
            details.append("=== 原始请求 ===\n");
            details.append(vuln.getOriginalRequest()).append("\n\n");
        }

        if (vuln.getResponse() != null) {
            details.append("=== 服务器响应 ===\n");
            details.append(vuln.getResponse()).append("\n");
        }

        detailsArea.setText(details.toString());
        detailsArea.setCaretPosition(0);
    }
    
    /**
     * 获取中文严重程度
     */
    private String getSeverityInChinese(Vulnerability.Severity severity) {
        switch (severity) {
            case LOW: return "低危";
            case MEDIUM: return "中危";
            case HIGH: return "高危";
            case CRITICAL: return "严重";
            default: return severity.getDisplayName();
        }
    }

    /**
     * 获取中文漏洞描述
     */
    private String getDescriptionInChinese(Vulnerability vuln) {
        switch (vuln.getType()) {
            case SQL_INJECTION:
                return "检测到潜在的SQL注入漏洞。应用程序可能容易受到SQL注入攻击，攻击者可能通过恶意SQL语句获取、修改或删除数据库中的数据。";
            case COMMAND_INJECTION:
                return "检测到潜在的命令注入漏洞。应用程序可能执行任意系统命令，攻击者可能通过此漏洞控制服务器系统。";
            case PATH_TRAVERSAL:
                return "检测到潜在的路径遍历漏洞。应用程序可能允许访问未授权的文件，攻击者可能读取系统敏感文件。";
            case XSS:
                return "检测到潜在的跨站脚本(XSS)漏洞。攻击者可能在用户浏览器中执行恶意脚本代码。";
            case PARAMETER_POLLUTION:
                return "检测到参数污染问题。发现多个同名参数，可能导致应用程序逻辑混乱。";
            case SENSITIVE_DATA_EXPOSURE:
                return "检测到敏感数据暴露。响应中包含可能的敏感信息。";
            default:
                return vuln.getDescription();
        }
    }

    /**
     * 获取中文修复建议
     */
    private String getRecommendationInChinese(Vulnerability vuln) {
        switch (vuln.getType()) {
            case SQL_INJECTION:
                return "使用参数化查询或预编译语句。验证和过滤所有用户输入。避免直接拼接SQL语句。";
            case COMMAND_INJECTION:
                return "避免使用用户输入执行系统命令。使用安全的API接口，严格验证输入内容。";
            case PATH_TRAVERSAL:
                return "验证文件路径，使用白名单方式。避免直接使用用户输入访问文件系统。";
            case XSS:
                return "对输出数据进行编码，验证输入内容。使用内容安全策略(CSP)。";
            case PARAMETER_POLLUTION:
                return "实现正确的参数处理和验证逻辑，确保参数唯一性。";
            case SENSITIVE_DATA_EXPOSURE:
                return "从响应中移除敏感信息。实施适当的访问控制机制。";
            default:
                return vuln.getRecommendation();
        }
    }

    /**
     * 更新统计信息
     */
    private void updateStats() {
        SwingUtilities.invokeLater(() -> {
            int total = vulnerabilityManager.getTotalCount();
            int high = vulnerabilityManager.getHighRiskCount();
            int medium = vulnerabilityManager.getMediumRiskCount();
            int low = vulnerabilityManager.getLowRiskCount();

            totalLabel.setText("总计: " + total);
            highLabel.setText("高危: " + high);
            mediumLabel.setText("中危: " + medium);
            lowLabel.setText("低危: " + low);

            // 设置颜色
            highLabel.setForeground(high > 0 ? Color.RED : Color.BLACK);
            mediumLabel.setForeground(medium > 0 ? Color.ORANGE : Color.BLACK);
            lowLabel.setForeground(low > 0 ? Color.BLUE : Color.BLACK);
        });
    }
    
    /**
     * 导出报告
     */
    private void exportReport() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("导出漏洞报告");
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("HTML文件", "html"));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String timestamp = sdf.format(new Date());
        fileChooser.setSelectedFile(new File("漏洞扫描报告_" + timestamp + ".html"));

        if (fileChooser.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
            File file = fileChooser.getSelectedFile();
            try {
                generateHtmlReport(file);
                JOptionPane.showMessageDialog(this, "报告已成功导出到: " + file.getAbsolutePath());
            } catch (IOException e) {
                JOptionPane.showMessageDialog(this, "导出报告时出错: " + e.getMessage(),
                    "导出错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * 生成HTML报告
     */
    private void generateHtmlReport(File file) throws IOException {
        List<Vulnerability> vulnerabilities = vulnerabilityManager.getAllVulnerabilities();
        Map<VulnerabilityType, Integer> stats = vulnerabilityManager.getVulnerabilityStats();
        
        try (FileWriter writer = new FileWriter(file)) {
            writer.write("<!DOCTYPE html>\n<html>\n<head>\n");
            writer.write("<meta charset=\"UTF-8\">\n");
            writer.write("<title>参数安全扫描报告</title>\n");
            writer.write("<style>\n");
            writer.write("body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }\n");
            writer.write("h1, h2 { color: #333; }\n");
            writer.write("table { border-collapse: collapse; width: 100%; margin: 20px 0; }\n");
            writer.write("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n");
            writer.write("th { background-color: #f2f2f2; }\n");
            writer.write(".high { color: red; font-weight: bold; }\n");
            writer.write(".medium { color: orange; font-weight: bold; }\n");
            writer.write(".low { color: blue; }\n");
            writer.write(".critical { color: darkred; font-weight: bold; }\n");
            writer.write(".details { margin: 10px 0; padding: 10px; background-color: #f9f9f9; }\n");
            writer.write("</style>\n</head>\n<body>\n");

            writer.write("<h1>参数安全扫描报告</h1>\n");
            writer.write("<p>报告生成时间: " + new Date() + "</p>\n");

            // 统计信息
            writer.write("<h2>扫描结果汇总</h2>\n");
            writer.write("<p>发现漏洞总数: " + vulnerabilities.size() + "</p>\n");
            writer.write("<ul>\n");
            for (Map.Entry<VulnerabilityType, Integer> entry : stats.entrySet()) {
                if (entry.getValue() > 0) {
                    writer.write("<li>" + entry.getKey().getDisplayName() + ": " + entry.getValue() + "</li>\n");
                }
            }
            writer.write("</ul>\n");
            
            // 漏洞详情
            writer.write("<h2>漏洞详细信息</h2>\n");
            writer.write("<table>\n");
            writer.write("<tr><th>漏洞类型</th><th>严重程度</th><th>URL地址</th><th>参数名称</th><th>发现时间</th></tr>\n");

            for (Vulnerability vuln : vulnerabilities) {
                String severityClass = vuln.getSeverity().name().toLowerCase();
                writer.write("<tr>\n");
                writer.write("<td>" + vuln.getType().getDisplayName() + "</td>\n");
                writer.write("<td class=\"" + severityClass + "\">" + getSeverityInChinese(vuln.getSeverity()) + "</td>\n");
                writer.write("<td>" + escapeHtml(vuln.getUrl()) + "</td>\n");
                writer.write("<td>" + escapeHtml(vuln.getParameter()) + "</td>\n");
                writer.write("<td>" + vuln.getFormattedDiscoveryTime() + "</td>\n");
                writer.write("</tr>\n");

                // 详细信息
                writer.write("<tr><td colspan=\"5\">\n");
                writer.write("<div class=\"details\">\n");
                writer.write("<strong>漏洞描述:</strong> " + escapeHtml(getDescriptionInChinese(vuln)) + "<br>\n");
                writer.write("<strong>漏洞证据:</strong> " + escapeHtml(vuln.getEvidence()) + "<br>\n");
                writer.write("<strong>修复建议:</strong> " + escapeHtml(getRecommendationInChinese(vuln)) + "\n");
                writer.write("</div>\n");
                writer.write("</td></tr>\n");
            }
            
            writer.write("</table>\n");
            writer.write("</body>\n</html>");
        }
    }
    
    /**
     * 显示域名白名单管理对话框
     */
    private void showDomainWhitelistDialog() {
        JDialog dialog = new JDialog((Frame) SwingUtilities.getWindowAncestor(this), "域名白名单管理", true);
        dialog.setSize(600, 500);
        dialog.setLocationRelativeTo(this);

        JPanel mainPanel = new JPanel(new BorderLayout());

        // 说明文本
        JLabel infoLabel = new JLabel("<html><b>域名白名单</b> - 以下域名将不会被扫描：</html>");
        infoLabel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        mainPanel.add(infoLabel, BorderLayout.NORTH);

        // 域名列表
        JTextArea domainListArea = new JTextArea();
        domainListArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        domainListArea.setText(plugin.getDomainWhitelistManager().exportWhitelistAsString().replace(", ", "\n"));
        domainListArea.setLineWrap(true);
        domainListArea.setWrapStyleWord(true);

        JScrollPane scrollPane = new JScrollPane(domainListArea);
        scrollPane.setBorder(BorderFactory.createTitledBorder("域名列表（每行一个域名）"));
        mainPanel.add(scrollPane, BorderLayout.CENTER);

        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());

        JButton saveButton = new JButton("保存");
        saveButton.addActionListener(e -> {
            // 清空现有白名单
            plugin.getDomainWhitelistManager().clearWhitelist();

            // 添加新的域名
            String[] domains = domainListArea.getText().split("\\s+");
            for (String domain : domains) {
                domain = domain.trim();
                if (!domain.isEmpty()) {
                    plugin.getDomainWhitelistManager().addDomain(domain);
                }
            }

            JOptionPane.showMessageDialog(dialog,
                "域名白名单已更新，共 " + plugin.getDomainWhitelistManager().getWhitelistSize() + " 个域名");
            dialog.dispose();
        });

        JButton resetButton = new JButton("重置为默认");
        resetButton.addActionListener(e -> {
            int result = JOptionPane.showConfirmDialog(dialog,
                "确定要重置为默认白名单吗？", "确认重置", JOptionPane.YES_NO_OPTION);
            if (result == JOptionPane.YES_OPTION) {
                plugin.getDomainWhitelistManager().resetToDefault();
                domainListArea.setText(plugin.getDomainWhitelistManager().exportWhitelistAsString().replace(", ", "\n"));
            }
        });

        JButton cancelButton = new JButton("取消");
        cancelButton.addActionListener(e -> dialog.dispose());

        buttonPanel.add(saveButton);
        buttonPanel.add(resetButton);
        buttonPanel.add(cancelButton);

        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        dialog.add(mainPanel);
        dialog.setVisible(true);
    }

    /**
     * 显示敏感信息规则管理对话框
     */
    private void showSensitiveRulesDialog() {
        JDialog dialog = new JDialog((Frame) SwingUtilities.getWindowAncestor(this), "敏感信息检测规则管理", true);
        dialog.setSize(1000, 700);
        dialog.setLocationRelativeTo(this);

        JPanel mainPanel = new JPanel(new BorderLayout());

        // 说明文本 - 修复HTML标签问题
        JLabel infoLabel = new JLabel("敏感信息检测规则 - 管理用于检测敏感信息的正则表达式规则");
        infoLabel.setFont(infoLabel.getFont().deriveFont(Font.BOLD, 14f));
        infoLabel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        mainPanel.add(infoLabel, BorderLayout.NORTH);

        // 创建统一的规则列表面板
        JPanel rulesPanel = createUnifiedRulesPanel();
        mainPanel.add(rulesPanel, BorderLayout.CENTER);

        // 关闭按钮
        JPanel buttonPanel = new JPanel(new FlowLayout());
        JButton closeButton = new JButton("关闭");
        closeButton.addActionListener(e -> dialog.dispose());
        buttonPanel.add(closeButton);

        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        dialog.add(mainPanel);
        dialog.setVisible(true);
    }

    /**
     * 创建统一的规则列表面板
     */
    private JPanel createUnifiedRulesPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // 规则列表表格
        String[] columnNames = {"类型", "规则名称", "正则表达式", "描述", "严重程度"};
        DefaultTableModel model = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        JTable table = new JTable(model);
        table.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        // 设置列宽
        table.getColumnModel().getColumn(0).setPreferredWidth(60);  // 类型
        table.getColumnModel().getColumn(1).setPreferredWidth(150); // 规则名称
        table.getColumnModel().getColumn(2).setPreferredWidth(300); // 正则表达式
        table.getColumnModel().getColumn(3).setPreferredWidth(200); // 描述
        table.getColumnModel().getColumn(4).setPreferredWidth(80);  // 严重程度

        // 加载所有规则到表格
        loadAllRulesToTable(model);

        JScrollPane scrollPane = new JScrollPane(table);
        scrollPane.setBorder(BorderFactory.createTitledBorder("检测规则列表（共 " + model.getRowCount() + " 条）"));

        panel.add(scrollPane, BorderLayout.CENTER);

        // 添加自定义规则面板
        JPanel addPanel = createAddRulePanel(model, table);
        panel.add(addPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * 加载所有规则到表格
     */
    private void loadAllRulesToTable(DefaultTableModel model) {
        // 添加内置规则
        addBuiltinRulesToTable(model);

        // 添加自定义规则
        addCustomRulesToTable(model);
    }

    /**
     * 添加内置规则到表格
     */
    private void addBuiltinRulesToTable(DefaultTableModel model) {
        try {
            // 获取敏感信息检测器
            SensitiveInfoDetector detector = plugin.getHttpListener().getSensitiveInfoDetector();
            List<SensitiveInfoDetector.SensitivePattern> allPatterns = detector.getAllPatterns();

            // 添加内置规则到表格
            for (SensitiveInfoDetector.SensitivePattern pattern : allPatterns) {
                if (pattern.isBuiltin) {
                    String severityText = getSeverityInChinese(pattern.severity);
                    model.addRow(new Object[]{"内置", pattern.name, pattern.pattern.pattern(), pattern.description, severityText});
                }
            }

        } catch (Exception e) {
            // 如果获取失败，使用备用的静态规则
            plugin.getCallbacks().printError("获取内置规则失败，使用备用规则: " + e.getMessage());

            // 备用的基础规则
            Object[][] backupRules = {
                {"内置", "AWS Access Key", "AKIA[0-9A-Z]{16}", "AWS访问密钥", "高危"},
                {"内置", "Google API Key", "AIza[0-9A-Za-z\\-_]{35}", "Google API密钥", "高危"},
                {"内置", "JWT Token", "eyJ[A-Za-z0-9_/+-]*\\.eyJ[A-Za-z0-9_/+-]*\\.[A-Za-z0-9._/+-]*", "JWT令牌", "中危"},
                {"内置", "Email Address", "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}", "邮箱地址", "低危"}
            };

            for (Object[] rule : backupRules) {
                model.addRow(rule);
            }
        }
    }

    /**
     * 添加自定义规则到表格
     */
    private void addCustomRulesToTable(DefaultTableModel model) {
        try {
            // 获取敏感信息检测器
            SensitiveInfoDetector detector = plugin.getHttpListener().getSensitiveInfoDetector();
            List<SensitiveInfoDetector.SensitivePattern> customPatterns = detector.getCustomPatterns();

            // 添加自定义规则到表格
            for (SensitiveInfoDetector.SensitivePattern pattern : customPatterns) {
                String severityText = getSeverityInChinese(pattern.severity);
                model.addRow(new Object[]{"自定义", pattern.name, pattern.pattern.pattern(), pattern.description, severityText});
            }

        } catch (Exception e) {
            // 如果获取失败，暂时不添加自定义规则
            plugin.getCallbacks().printError("加载自定义规则到界面失败: " + e.getMessage());
        }
    }

    /**
     * 创建添加规则面板
     */
    private JPanel createAddRulePanel(DefaultTableModel model, JTable table) {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder("添加自定义规则"));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);

        // 规则名称
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("规则名称:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JTextField nameField = new JTextField(20);
        panel.add(nameField, gbc);

        // 正则表达式
        gbc.gridx = 2; gbc.gridy = 0; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("正则表达式:"), gbc);
        gbc.gridx = 3; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 2.0;
        JTextField regexField = new JTextField(30);
        panel.add(regexField, gbc);

        // 描述
        gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("描述:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JTextField descField = new JTextField(20);
        panel.add(descField, gbc);

        // 严重程度
        gbc.gridx = 2; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("严重程度:"), gbc);
        gbc.gridx = 3; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0;
        JComboBox<String> severityCombo = new JComboBox<>(new String[]{"低危", "中危", "高危", "严重"});
        severityCombo.setSelectedIndex(1); // 默认中危
        panel.add(severityCombo, gbc);

        // 按钮
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 4; gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.CENTER; gbc.weightx = 0;
        JPanel buttonPanel = new JPanel(new FlowLayout());

        JButton addButton = new JButton("添加规则");
        addButton.addActionListener(e -> {
            String name = nameField.getText().trim();
            String regex = regexField.getText().trim();
            String desc = descField.getText().trim();
            String severity = (String) severityCombo.getSelectedItem();

            if (name.isEmpty() || regex.isEmpty() || desc.isEmpty()) {
                JOptionPane.showMessageDialog(panel, "请填写所有字段", "输入错误", JOptionPane.ERROR_MESSAGE);
                return;
            }

            try {
                // 测试正则表达式
                Pattern.compile(regex);

                // 添加到表格
                model.addRow(new Object[]{"自定义", name, regex, desc, severity});

                // 添加到敏感信息检测器
                addCustomRuleToDetector(name, regex, desc, severity);

                // 清空输入框
                nameField.setText("");
                regexField.setText("");
                descField.setText("");
                severityCombo.setSelectedIndex(1);

                // 更新表格标题
                updateTableTitle(table, model.getRowCount());

                JOptionPane.showMessageDialog(panel, "规则添加成功！", "成功", JOptionPane.INFORMATION_MESSAGE);

            } catch (Exception ex) {
                JOptionPane.showMessageDialog(panel, "正则表达式格式错误: " + ex.getMessage(),
                    "正则表达式错误", JOptionPane.ERROR_MESSAGE);
            }
        });

        JButton removeButton = new JButton("删除选中");
        removeButton.addActionListener(e -> {
            int selectedRow = table.getSelectedRow();
            if (selectedRow >= 0) {
                // 检查是否为内置规则
                String type = (String) model.getValueAt(selectedRow, 0);
                if ("内置".equals(type)) {
                    JOptionPane.showMessageDialog(panel, "不能删除内置规则", "操作错误", JOptionPane.WARNING_MESSAGE);
                    return;
                }

                String ruleName = (String) model.getValueAt(selectedRow, 1);
                model.removeRow(selectedRow);

                // 从敏感信息检测器中移除
                removeCustomRuleFromDetector(ruleName);

                // 更新表格标题
                updateTableTitle(table, model.getRowCount());

                JOptionPane.showMessageDialog(panel, "规则删除成功！", "成功", JOptionPane.INFORMATION_MESSAGE);
            } else {
                JOptionPane.showMessageDialog(panel, "请先选择要删除的规则", "提示", JOptionPane.WARNING_MESSAGE);
            }
        });

        buttonPanel.add(addButton);
        buttonPanel.add(removeButton);
        panel.add(buttonPanel, gbc);

        return panel;
    }

    /**
     * 添加自定义规则到检测器
     */
    private void addCustomRuleToDetector(String name, String regex, String desc, String severity) {
        try {
            // 转换严重程度
            Vulnerability.Severity sev = convertSeverity(severity);

            // 通过插件获取敏感信息检测器并添加规则
            SensitiveInfoDetector detector = plugin.getHttpListener().getSensitiveInfoDetector();
            detector.addCustomPattern(name, regex, desc, sev);

        } catch (Exception e) {
            plugin.getCallbacks().printError("添加自定义规则到检测器失败: " + e.getMessage());
        }
    }

    /**
     * 从检测器中移除自定义规则
     */
    private void removeCustomRuleFromDetector(String ruleName) {
        try {
            SensitiveInfoDetector detector = plugin.getHttpListener().getSensitiveInfoDetector();
            detector.removeCustomPattern(ruleName);
        } catch (Exception e) {
            plugin.getCallbacks().printError("从检测器移除自定义规则失败: " + e.getMessage());
        }
    }

    /**
     * 转换严重程度
     */
    private Vulnerability.Severity convertSeverity(String severity) {
        switch (severity) {
            case "低危": return Vulnerability.Severity.LOW;
            case "中危": return Vulnerability.Severity.MEDIUM;
            case "高危": return Vulnerability.Severity.HIGH;
            case "严重": return Vulnerability.Severity.CRITICAL;
            default: return Vulnerability.Severity.MEDIUM;
        }
    }

    /**
     * 更新表格标题
     */
    private void updateTableTitle(JTable table, int count) {
        JScrollPane scrollPane = (JScrollPane) table.getParent().getParent();
        scrollPane.setBorder(BorderFactory.createTitledBorder("检测规则列表（共 " + count + " 条）"));
    }

    /**
     * HTML转义
     */
    private String escapeHtml(String text) {
        if (text == null) return "";
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#x27;");
    }

    @Override
    public void onVulnerabilityFound(Vulnerability vulnerability) {
        SwingUtilities.invokeLater(() -> {
            // 添加到表格
            Object[] row = {
                vulnerability.getType().getDisplayName(),
                getSeverityInChinese(vulnerability.getSeverity()),
                vulnerability.getUrl(),
                vulnerability.getParameter(),
                vulnerability.getFormattedDiscoveryTime()
            };
            tableModel.addRow(row);

            // 更新统计
            updateStats();
        });
    }

    @Override
    public void onVulnerabilitiesCleared() {
        SwingUtilities.invokeLater(() -> {
            tableModel.setRowCount(0);
            detailsArea.setText("请选择一个漏洞查看详细信息...");
            updateStats();
        });
    }
}
