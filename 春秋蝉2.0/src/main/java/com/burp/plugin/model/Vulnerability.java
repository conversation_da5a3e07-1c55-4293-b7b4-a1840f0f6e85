package com.burp.plugin.model;

import java.util.Date;
import java.text.SimpleDateFormat;

/**
 * 漏洞信息模型类
 */
public class Vulnerability {
    
    private final String id;
    private final VulnerabilityType type;
    private final String url;
    private final String parameter;
    private final String payload;
    private final String evidence;
    private final Severity severity;
    private final Date discoveredAt;
    private final String method;
    private final String originalRequest;
    private final String response;
    
    public enum Severity {
        LOW("Low", 1),
        MEDIUM("Medium", 2), 
        HIGH("High", 3),
        CRITICAL("Critical", 4);
        
        private final String displayName;
        private final int level;
        
        Severity(String displayName, int level) {
            this.displayName = displayName;
            this.level = level;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public int getLevel() {
            return level;
        }
    }
    
    private Vulnerability(Builder builder) {
        this.id = generateId();
        this.type = builder.type;
        this.url = builder.url;
        this.parameter = builder.parameter;
        this.payload = builder.payload;
        this.evidence = builder.evidence;
        this.severity = builder.severity;
        this.discoveredAt = new Date();
        this.method = builder.method;
        this.originalRequest = builder.originalRequest;
        this.response = builder.response;
    }
    
    private String generateId() {
        return "VULN-" + System.currentTimeMillis() + "-" + (int)(Math.random() * 1000);
    }
    
    // Getters
    public String getId() { return id; }
    public VulnerabilityType getType() { return type; }
    public String getUrl() { return url; }
    public String getParameter() { return parameter; }
    public String getPayload() { return payload; }
    public String getEvidence() { return evidence; }
    public Severity getSeverity() { return severity; }
    public Date getDiscoveredAt() { return discoveredAt; }
    public String getMethod() { return method; }
    public String getOriginalRequest() { return originalRequest; }
    public String getResponse() { return response; }
    
    public String getFormattedDiscoveryTime() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(discoveredAt);
    }
    
    /**
     * 获取漏洞描述
     */
    public String getDescription() {
        switch (type) {
            case SQL_INJECTION:
                return "Potential SQL Injection vulnerability detected. The application may be vulnerable to SQL injection attacks.";
            case COMMAND_INJECTION:
                return "Potential Command Injection vulnerability detected. The application may execute arbitrary system commands.";
            case PATH_TRAVERSAL:
                return "Potential Path Traversal vulnerability detected. The application may allow access to unauthorized files.";
            case XSS:
                return "Potential Cross-Site Scripting (XSS) vulnerability detected.";
            case PARAMETER_POLLUTION:
                return "Parameter pollution detected. Multiple parameters with the same name found.";
            case SENSITIVE_DATA_EXPOSURE:
                return "Sensitive data exposure detected in response.";
            default:
                return "Security vulnerability detected.";
        }
    }
    
    /**
     * 获取修复建议
     */
    public String getRecommendation() {
        switch (type) {
            case SQL_INJECTION:
                return "Use parameterized queries or prepared statements. Validate and sanitize all user inputs.";
            case COMMAND_INJECTION:
                return "Avoid executing system commands with user input. Use safe APIs and validate inputs strictly.";
            case PATH_TRAVERSAL:
                return "Validate file paths and use whitelist approach. Avoid direct file access with user input.";
            case XSS:
                return "Encode output data and validate inputs. Use Content Security Policy (CSP).";
            case PARAMETER_POLLUTION:
                return "Implement proper parameter handling and validation logic.";
            case SENSITIVE_DATA_EXPOSURE:
                return "Remove sensitive information from responses. Implement proper access controls.";
            default:
                return "Review and fix the identified security issue.";
        }
    }
    
    @Override
    public String toString() {
        return String.format("[%s] %s - %s:%s (%s)", 
                severity.getDisplayName(), 
                type.getDisplayName(), 
                url, 
                parameter, 
                getFormattedDiscoveryTime());
    }
    
    /**
     * Builder模式构造器
     */
    public static class Builder {
        private VulnerabilityType type;
        private String url;
        private String parameter;
        private String payload;
        private String evidence;
        private Severity severity = Severity.MEDIUM;
        private String method = "GET";
        private String originalRequest;
        private String response;
        
        public Builder type(VulnerabilityType type) {
            this.type = type;
            return this;
        }
        
        public Builder url(String url) {
            this.url = url;
            return this;
        }
        
        public Builder parameter(String parameter) {
            this.parameter = parameter;
            return this;
        }
        
        public Builder payload(String payload) {
            this.payload = payload;
            return this;
        }
        
        public Builder evidence(String evidence) {
            this.evidence = evidence;
            return this;
        }
        
        public Builder severity(Severity severity) {
            this.severity = severity;
            return this;
        }
        
        public Builder method(String method) {
            this.method = method;
            return this;
        }
        
        public Builder originalRequest(String originalRequest) {
            this.originalRequest = originalRequest;
            return this;
        }
        
        public Builder response(String response) {
            this.response = response;
            return this;
        }
        
        public Vulnerability build() {
            if (type == null || url == null || parameter == null) {
                throw new IllegalArgumentException("Type, URL and parameter are required");
            }
            return new Vulnerability(this);
        }
    }
}
