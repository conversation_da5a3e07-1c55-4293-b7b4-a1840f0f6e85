# Parameter Security Scanner - 安装指南

## 快速开始

### 1. 下载插件

插件已经编译完成，JAR文件位于：
```
target/parameter-security-scanner-1.0.0.jar
```

文件大小：约4.3MB（包含所有依赖）

### 2. 在Burp Suite中安装

#### 步骤：
1. 打开Burp Suite Professional或Community版本
2. 进入 **Extender** 标签页
3. 点击 **Extensions** 子标签
4. 点击 **Add** 按钮
5. 在弹出的对话框中：
   - Extension type: 选择 **Java**
   - Extension file: 选择 `target/parameter-security-scanner-1.0.0.jar`
6. 点击 **Next** 按钮
7. 等待插件加载完成

#### 验证安装：
- 在Extensions列表中应该看到 "Parameter Security Scanner"
- 在Burp Suite主界面应该出现新的 "Param Scanner" 标签页
- Output面板应该显示：`Parameter Security Scanner loaded successfully!`

### 3. 基本配置

#### 插件界面说明：
- **Plugin Controls**: 插件功能开关
  - Enable Plugin: 启用/禁用整个插件
  - SQL Injection Testing: SQL注入检测开关
  - API Fuzzing: API模糊测试开关
  - Crawler: 同域爬虫开关（默认关闭）

- **Discovered Vulnerabilities**: 漏洞列表表格
  - 显示发现的所有安全漏洞
  - 可按类型、严重程度等排序

- **Vulnerability Details**: 漏洞详情面板
  - 显示选中漏洞的详细信息
  - 包括载荷、证据、修复建议等

- **状态栏**: 显示漏洞统计信息
  - Total: 总漏洞数
  - High/Medium/Low: 各严重程度漏洞数

### 4. 使用流程

#### 基本使用：
1. **启用插件**: 确保 "Enable Plugin" 已勾选
2. **选择功能**: 根据需要启用相应的检测功能
3. **配置代理**: 确保浏览器流量通过Burp Suite代理
4. **浏览目标**: 正常浏览目标Web应用程序
5. **查看结果**: 在插件界面查看发现的漏洞
6. **导出报告**: 使用 "Export Report" 生成HTML报告

#### 推荐设置：
- **初次使用**: 只启用 "SQL Injection Testing"
- **深度测试**: 启用 "SQL Injection Testing" + "API Fuzzing"
- **全面扫描**: 启用所有功能（注意爬虫可能产生大量请求）

### 5. 功能详解

#### SQL注入检测：
- 自动检测URL参数、POST参数中的SQL注入漏洞
- 支持多种数据库错误模式识别
- 使用多种注入载荷进行测试

#### API模糊测试：
- 命令注入检测
- 路径遍历检测
- XSS（跨站脚本）检测
- 支持Linux/Windows环境

#### 同域爬虫：
- 仅爬取同域名页面
- 可配置爬取深度（默认2层）
- 自动过滤静态资源和危险操作
- 限制爬取URL数量（默认50个）

### 6. 注意事项

#### 性能考虑：
- 插件使用异步处理，不会阻塞Burp Suite
- 大型应用建议关闭爬虫功能
- 可选择性启用检测模块以提高性能

#### 安全考虑：
- 仅在授权的测试环境中使用
- 爬虫功能默认关闭，需手动启用
- 避免在生产环境中使用

#### 兼容性：
- 支持Burp Suite Professional和Community版本
- 需要Java 8或更高版本
- 已在Windows、macOS、Linux上测试

### 7. 故障排除

#### 常见问题：

**Q: 插件加载失败**
A: 检查Java版本是否为8或更高，确保JAR文件完整

**Q: 没有检测到漏洞**
A: 确保插件已启用，目标应用有可测试的参数

**Q: 插件运行缓慢**
A: 关闭不需要的检测功能，特别是爬虫功能

**Q: 误报过多**
A: 插件使用保守的检测策略，建议手工验证结果

#### 日志查看：
- Burp Suite的Output面板会显示插件运行日志
- Errors面板会显示错误信息

### 8. 高级配置

#### 爬虫设置：
可以通过修改源码调整以下参数：
- 最大爬取深度：1-5层
- 最大URL数量：10-200个
- 爬取延迟：100ms-5s

#### 检测载荷：
可以通过修改源码自定义：
- SQL注入载荷
- 命令注入载荷
- XSS载荷
- 路径遍历载荷

### 9. 更新和维护

#### 版本信息：
- 当前版本：1.0.0
- 发布日期：2025-08-01
- 兼容Burp Suite版本：2.x

#### 获取更新：
- 重新编译源码获取最新版本
- 关注项目更新日志

### 10. 技术支持

如遇到问题，请检查：
1. Java版本是否正确
2. Burp Suite版本是否兼容
3. JAR文件是否完整
4. 插件配置是否正确

更多技术细节请参考 README.md 文档。
