# 🔒 春秋蝉2.0 域名白名单持久化功能

## 📋 功能概述

春秋蝉2.0现在支持域名白名单的持久化存储，用户自定义的白名单域名会自动保存到本地文件，重启Burp Suite后会自动加载。

## 🆕 新增功能

### 1. **持久化存储**
- ✅ 自定义白名单域名自动保存到本地配置文件
- ✅ 重启Burp Suite后自动加载自定义域名
- ✅ 配置文件位置智能选择（JAR目录 > 用户目录 > 临时目录）

### 2. **域名分类管理**
- ✅ **内置域名**: 插件预设的常见服务域名，无法删除
- ✅ **自定义域名**: 用户添加的域名，支持增删改查

### 3. **全新UI界面**
- ✅ 选项卡式界面，分别管理内置和自定义域名
- ✅ 配置信息面板，显示详细的配置状态
- ✅ 一键添加、批量保存、清空功能

## 📁 配置文件位置

### 自动选择策略
1. **优先**: JAR文件同目录下的 `domain_whitelist.properties`
2. **备选**: 用户目录下的 `.burp_domain_whitelist.properties`
3. **最后**: 临时目录下的 `domain_whitelist.properties`

### 示例路径
```
# 如果JAR在 /Users/<USER>/Burp插件/春秋蝉2.0/target/
配置文件: /Users/<USER>/Burp插件/春秋蝉2.0/target/domain_whitelist.properties

# 如果无法确定JAR路径
配置文件: /Users/<USER>/.burp_domain_whitelist.properties
```

## 🎯 使用方法

### 1. **查看域名白名单**
1. 打开Burp Suite，加载春秋蝉2.0插件
2. 点击 "春秋蝉 2.0" 标签页
3. 点击 "域名白名单管理" 按钮

### 2. **添加自定义域名**
#### 方法一：单个添加
1. 在 "自定义白名单" 选项卡中
2. 点击 "添加域名" 按钮
3. 输入域名（如：example.com）
4. 自动保存到配置文件

#### 方法二：批量添加
1. 在 "自定义白名单" 选项卡的文本框中
2. 每行输入一个域名
3. 点击 "保存自定义域名" 按钮

### 3. **删除自定义域名**
1. 在文本框中删除对应的域名行
2. 点击 "保存自定义域名" 按钮
3. 或者点击 "清空自定义域名" 清空所有

### 4. **查看配置信息**
在 "配置信息" 选项卡中可以看到：
- 总域名数量统计
- 配置文件路径
- 配置文件是否存在
- 功能说明

## 📊 配置文件格式

```properties
# Burp Suite ChunQiu Chan - Custom Domain Whitelist
domains.count=3
domain.0=example.com
domain.1=test.com
domain.2=mysite.org
```

## 🔧 技术实现

### 核心类
- `DomainWhitelistManager`: 域名白名单管理器
- `MainPanel`: UI界面管理

### 关键方法
```java
// 保存自定义白名单
private void saveCustomWhitelist()

// 加载自定义白名单  
private void loadCustomWhitelist()

// 获取自定义域名
public Set<String> getCustomDomains()

// 清空自定义白名单
public void clearCustomWhitelist()
```

## 🛡️ 安全特性

### 1. **内置域名保护**
- 内置域名无法被删除
- 确保常见服务域名始终在白名单中

### 2. **配置文件安全**
- 配置文件仅包含域名信息
- 不包含敏感数据
- 支持手动编辑

### 3. **错误处理**
- 配置文件读写异常处理
- 无效域名自动过滤
- 降级策略确保功能可用

## 📈 性能优化

### 1. **内存效率**
- 使用ConcurrentHashMap确保线程安全
- 域名去重和规范化处理

### 2. **IO优化**
- 仅在域名变更时写入文件
- 启动时一次性加载配置

### 3. **UI响应**
- 异步保存配置文件
- 实时更新界面显示

## 🔍 故障排除

### 问题1：配置文件无法保存
**原因**: 目录权限不足
**解决**: 检查JAR文件目录的写权限，或手动指定配置文件路径

### 问题2：重启后自定义域名丢失
**原因**: 配置文件路径变更或文件损坏
**解决**: 查看控制台输出的配置文件路径，确认文件是否存在

### 问题3：无法删除某些域名
**原因**: 尝试删除内置域名
**解决**: 内置域名无法删除，只能删除自定义域名

## 📝 使用示例

### 添加公司内网域名
```
# 添加以下域名到自定义白名单
internal.company.com
dev.company.com
staging.company.com
admin.company.com
```

### 添加测试环境域名
```
# 添加测试环境域名
localhost
127.0.0.1
test.local
dev.local
```

## 🎉 升级说明

### 从春秋蝉1.0升级
1. 卸载春秋蝉1.0插件
2. 安装春秋蝉2.0插件
3. 重新添加自定义白名单域名（1.0版本的白名单不会自动迁移）

### 配置迁移
如果需要迁移大量域名：
1. 导出1.0版本的域名列表
2. 在2.0版本中批量添加
3. 验证配置文件正确保存

## 🔮 未来计划

- [ ] 支持域名分组管理
- [ ] 支持正则表达式匹配
- [ ] 支持导入/导出功能
- [ ] 支持云端同步配置

---

**春秋蝉2.0** - 让安全测试更智能，让配置管理更便捷！ 🦗✨
