# Parameter Security Scanner Plugin Configuration

# Plugin Information
plugin.name=Parameter Security Scanner
plugin.version=1.0.0
plugin.author=Security Team
plugin.description=Advanced parameter security testing plugin for Burp Suite

# Default Settings
scanner.enabled=true
sql.injection.enabled=true
api.fuzzing.enabled=true
crawler.enabled=false

# SQL Injection Settings
sql.injection.timeout=5000
sql.injection.max.payloads=20
sql.injection.delay=100

# API Fuzzing Settings
api.fuzzing.timeout=5000
api.fuzzing.max.payloads=15
api.fuzzing.delay=100

# Crawler Settings
crawler.max.depth=2
crawler.max.urls=50
crawler.delay=1000
crawler.same.domain.only=true

# Performance Settings
thread.pool.size=5
max.concurrent.requests=10
request.timeout=10000

# Logging Settings
log.level=INFO
log.max.entries=1000

# UI Settings
ui.refresh.interval=1000
ui.max.table.rows=500

# Export Settings
export.include.request=true
export.include.response=true
export.max.response.size=2000

# Domain Whitelist Settings
domain.whitelist.enabled=true
domain.whitelist.custom=
domain.whitelist.include.subdomains=true
