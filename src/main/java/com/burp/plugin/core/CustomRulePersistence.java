package com.burp.plugin.core;

import com.burp.plugin.model.Vulnerability;
import burp.IBurpExtenderCallbacks;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * 自定义规则持久化管理器
 * 将自定义敏感信息检测规则保存到JAR文件同目录的配置文件中
 */
public class CustomRulePersistence {
    
    private final IBurpExtenderCallbacks callbacks;
    private final String configFileName = "sensitive_rules.properties";
    private File configFile;
    
    public CustomRulePersistence(IBurpExtenderCallbacks callbacks) {
        this.callbacks = callbacks;
        initializeConfigFile();
    }
    
    /**
     * 初始化配置文件路径
     */
    private void initializeConfigFile() {
        try {
            // 获取JAR文件所在目录
            String jarPath = getJarPath();
            if (jarPath != null) {
                File jarDir = new File(jarPath).getParentFile();
                configFile = new File(jarDir, configFileName);
            } else {
                // 如果无法获取JAR路径，使用用户目录
                String userHome = System.getProperty("user.home");
                configFile = new File(userHome, ".burp_sensitive_rules.properties");
            }
            
            callbacks.printOutput("自定义规则配置文件路径: " + configFile.getAbsolutePath());
            
        } catch (Exception e) {
            callbacks.printError("初始化配置文件失败: " + e.getMessage());
            // 使用临时目录作为备选
            String tempDir = System.getProperty("java.io.tmpdir");
            configFile = new File(tempDir, configFileName);
        }
    }
    
    /**
     * 获取JAR文件路径
     */
    private String getJarPath() {
        try {
            // 尝试通过类加载器获取JAR路径
            String className = this.getClass().getName().replace('.', '/') + ".class";
            String classJar = this.getClass().getClassLoader().getResource(className).toString();
            
            if (classJar.startsWith("jar:")) {
                String jarPath = classJar.substring(4, classJar.indexOf("!/"));
                if (jarPath.startsWith("file:")) {
                    return jarPath.substring(5);
                }
                return jarPath;
            }
        } catch (Exception e) {
            // 忽略错误，返回null
        }
        return null;
    }
    
    /**
     * 保存自定义规则
     */
    public void saveCustomRules(List<CustomRule> rules) {
        Properties props = new Properties();
        
        try {
            // 保存规则数量
            props.setProperty("rules.count", String.valueOf(rules.size()));
            
            // 保存每个规则
            for (int i = 0; i < rules.size(); i++) {
                CustomRule rule = rules.get(i);
                String prefix = "rule." + i + ".";
                
                props.setProperty(prefix + "name", rule.name);
                props.setProperty(prefix + "regex", rule.regex);
                props.setProperty(prefix + "description", rule.description);
                props.setProperty(prefix + "severity", rule.severity.name());
            }
            
            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(configFile)) {
                props.store(fos, "Burp Suite Parameter Security Scanner - Custom Sensitive Info Rules");
            }
            
            callbacks.printOutput("已保存 " + rules.size() + " 个自定义规则到: " + configFile.getAbsolutePath());
            
        } catch (Exception e) {
            callbacks.printError("保存自定义规则失败: " + e.getMessage());
        }
    }
    
    /**
     * 加载自定义规则
     */
    public List<CustomRule> loadCustomRules() {
        List<CustomRule> rules = new ArrayList<>();
        
        if (!configFile.exists()) {
            callbacks.printOutput("配置文件不存在，将创建新的配置文件");
            return rules;
        }
        
        Properties props = new Properties();
        
        try (FileInputStream fis = new FileInputStream(configFile)) {
            props.load(fis);
            
            String countStr = props.getProperty("rules.count", "0");
            int count = Integer.parseInt(countStr);
            
            for (int i = 0; i < count; i++) {
                String prefix = "rule." + i + ".";
                
                String name = props.getProperty(prefix + "name");
                String regex = props.getProperty(prefix + "regex");
                String description = props.getProperty(prefix + "description");
                String severityStr = props.getProperty(prefix + "severity", "MEDIUM");
                
                if (name != null && regex != null && description != null) {
                    try {
                        Vulnerability.Severity severity = Vulnerability.Severity.valueOf(severityStr);
                        rules.add(new CustomRule(name, regex, description, severity));
                    } catch (Exception e) {
                        callbacks.printError("加载规则失败: " + name + " - " + e.getMessage());
                    }
                }
            }
            
            callbacks.printOutput("已加载 " + rules.size() + " 个自定义规则");
            
        } catch (Exception e) {
            callbacks.printError("加载自定义规则失败: " + e.getMessage());
        }
        
        return rules;
    }
    
    /**
     * 删除配置文件
     */
    public void deleteConfigFile() {
        try {
            if (configFile.exists()) {
                configFile.delete();
                callbacks.printOutput("已删除配置文件: " + configFile.getAbsolutePath());
            }
        } catch (Exception e) {
            callbacks.printError("删除配置文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取配置文件路径
     */
    public String getConfigFilePath() {
        return configFile.getAbsolutePath();
    }
    
    /**
     * 检查配置文件是否存在
     */
    public boolean configFileExists() {
        return configFile.exists();
    }
    
    /**
     * 自定义规则数据类
     */
    public static class CustomRule {
        public final String name;
        public final String regex;
        public final String description;
        public final Vulnerability.Severity severity;
        
        public CustomRule(String name, String regex, String description, Vulnerability.Severity severity) {
            this.name = name;
            this.regex = regex;
            this.description = description;
            this.severity = severity;
        }
        
        @Override
        public String toString() {
            return "CustomRule{" +
                    "name='" + name + '\'' +
                    ", regex='" + regex + '\'' +
                    ", description='" + description + '\'' +
                    ", severity=" + severity +
                    '}';
        }
    }
}
