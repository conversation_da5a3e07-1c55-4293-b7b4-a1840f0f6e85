package com.burp.plugin.core;

import burp.*;
import com.burp.plugin.model.Vulnerability;
import com.burp.plugin.model.VulnerabilityType;
import com.burp.plugin.analyzer.ParameterAnalyzer;
import com.burp.plugin.analyzer.SqlInjectionTester;
import com.burp.plugin.analyzer.ApiFuzzer;

import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * HTTP请求监听器 - 监听Proxy的HTTP History并进行安全测试
 */
public class HttpListener {
    
    private final IBurpExtenderCallbacks callbacks;
    private final IExtensionHelpers helpers;
    private final VulnerabilityManager vulnerabilityManager;
    private final CrawlerManager crawlerManager;
    
    // 分析器组件
    private final ParameterAnalyzer parameterAnalyzer;
    private final SqlInjectionTester sqlInjectionTester;
    private final ApiFuzzer apiFuzzer;
    
    // 线程池用于异步处理
    private final ExecutorService executorService;
    
    // 功能开关
    private boolean sqlInjectionTestEnabled = true;
    private boolean apiFuzzingEnabled = true;
    private boolean crawlerEnabled = false;
    
    public HttpListener(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers, 
                       VulnerabilityManager vulnerabilityManager, CrawlerManager crawlerManager) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.vulnerabilityManager = vulnerabilityManager;
        this.crawlerManager = crawlerManager;
        
        // 初始化分析器
        this.parameterAnalyzer = new ParameterAnalyzer(helpers);
        this.sqlInjectionTester = new SqlInjectionTester(callbacks, helpers, vulnerabilityManager);
        this.apiFuzzer = new ApiFuzzer(callbacks, helpers, vulnerabilityManager);
        
        // 创建线程池
        this.executorService = Executors.newFixedThreadPool(5);
    }
    
    /**
     * 处理HTTP消息
     */
    public void processHttpMessage(int toolFlag, boolean messageIsRequest, IHttpRequestResponse messageInfo) {
        if (messageIsRequest || messageInfo.getResponse() == null) {
            return;
        }
        
        // 异步处理以避免阻塞Burp
        executorService.submit(() -> {
            try {
                analyzeHttpMessage(messageInfo);
            } catch (Exception e) {
                callbacks.printError("Error analyzing HTTP message: " + e.getMessage());
            }
        });
    }
    
    /**
     * 分析HTTP消息
     */
    private void analyzeHttpMessage(IHttpRequestResponse messageInfo) {
        IRequestInfo requestInfo = helpers.analyzeRequest(messageInfo);
        IResponseInfo responseInfo = helpers.analyzeResponse(messageInfo.getResponse());

        URL url = requestInfo.getUrl();
        String method = requestInfo.getMethod();

        callbacks.printOutput("Analyzing request: " + method + " " + url.toString());

        // 跳过静态资源
        if (isStaticResource(url.getPath())) {
            callbacks.printOutput("Skipping static resource: " + url.getPath());
            return;
        }

        // 提取参数
        Map<String, List<String>> parameters = parameterAnalyzer.extractParameters(messageInfo);

        callbacks.printOutput("Found " + parameters.size() + " parameters: " + parameters.keySet());
        
        if (parameters.isEmpty()) {
            return;
        }
        
        // 检查参数污染
        checkParameterPollution(url.toString(), parameters);
        
        // 检查敏感数据暴露
        checkSensitiveDataExposure(messageInfo, url.toString());
        
        // 对每个参数进行安全测试
        for (Map.Entry<String, List<String>> entry : parameters.entrySet()) {
            String paramName = entry.getKey();
            List<String> paramValues = entry.getValue();
            
            if (paramValues.isEmpty()) {
                continue;
            }
            
            String paramValue = paramValues.get(0);
            String urlString = url.toString();
            
            // 检查是否已经测试过
            if (vulnerabilityManager.isAlreadyTested(urlString, paramName)) {
                continue;
            }
            
            // 标记为已测试
            vulnerabilityManager.markAsTested(urlString, paramName);
            
            // SQL注入测试
            if (sqlInjectionTestEnabled) {
                sqlInjectionTester.testParameter(messageInfo, paramName, paramValue);
            }
            
            // API模糊测试
            if (apiFuzzingEnabled) {
                apiFuzzer.testParameter(messageInfo, paramName, paramValue);
            }
        }
        
        // 爬虫功能
        if (crawlerEnabled) {
            crawlerManager.crawlFromResponse(messageInfo);
        }
    }
    
    /**
     * 检查参数污染
     */
    private void checkParameterPollution(String url, Map<String, List<String>> parameters) {
        for (Map.Entry<String, List<String>> entry : parameters.entrySet()) {
            String paramName = entry.getKey();
            List<String> values = entry.getValue();
            
            if (values.size() > 1) {
                // 发现参数污染
                Vulnerability vulnerability = new Vulnerability.Builder()
                        .type(VulnerabilityType.PARAMETER_POLLUTION)
                        .url(url)
                        .parameter(paramName)
                        .evidence("Multiple values found: " + values.toString())
                        .severity(Vulnerability.Severity.MEDIUM)
                        .build();
                
                vulnerabilityManager.addVulnerability(vulnerability);
            }
        }
    }
    
    /**
     * 检查敏感数据暴露
     */
    private void checkSensitiveDataExposure(IHttpRequestResponse messageInfo, String url) {
        String response = helpers.bytesToString(messageInfo.getResponse());
        
        // 检查常见的敏感信息模式
        String[] sensitivePatterns = {
            "password\\s*[=:]\\s*['\"]?\\w+['\"]?",
            "api[_-]?key\\s*[=:]\\s*['\"]?[\\w-]+['\"]?",
            "secret\\s*[=:]\\s*['\"]?\\w+['\"]?",
            "token\\s*[=:]\\s*['\"]?[\\w.-]+['\"]?",
            "\\b\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}\\b", // 信用卡号
            "\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b" // 邮箱
        };
        
        for (String pattern : sensitivePatterns) {
            if (response.matches("(?i).*" + pattern + ".*")) {
                Vulnerability vulnerability = new Vulnerability.Builder()
                        .type(VulnerabilityType.SENSITIVE_DATA_EXPOSURE)
                        .url(url)
                        .parameter("response")
                        .evidence("Sensitive data pattern detected: " + pattern)
                        .severity(Vulnerability.Severity.HIGH)
                        .response(response.length() > 1000 ? response.substring(0, 1000) + "..." : response)
                        .build();
                
                vulnerabilityManager.addVulnerability(vulnerability);
                break; // 只报告第一个匹配的模式
            }
        }
    }
    
    /**
     * 判断是否为静态资源
     */
    private boolean isStaticResource(String path) {
        String[] staticExtensions = {".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg", 
                                   ".woff", ".woff2", ".ttf", ".eot", ".pdf", ".zip", ".rar"};
        
        String lowerPath = path.toLowerCase();
        for (String ext : staticExtensions) {
            if (lowerPath.endsWith(ext)) {
                return true;
            }
        }
        return false;
    }
    
    // Setter方法用于控制功能开关
    public void setSqlInjectionTestEnabled(boolean enabled) {
        this.sqlInjectionTestEnabled = enabled;
    }
    
    public void setApiFuzzingEnabled(boolean enabled) {
        this.apiFuzzingEnabled = enabled;
    }
    
    public void setCrawlerEnabled(boolean enabled) {
        this.crawlerEnabled = enabled;
    }
    
    /**
     * 关闭资源
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
