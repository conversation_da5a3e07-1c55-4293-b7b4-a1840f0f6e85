package com.burp.plugin.core;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 域名白名单管理器 - 管理不需要扫描的域名列表
 */
public class DomainWhitelistManager {
    
    private final Set<String> whitelistedDomains;
    
    public DomainWhitelistManager() {
        this.whitelistedDomains = ConcurrentHashMap.newKeySet();
        initializeDefaultWhitelist();
    }
    
    /**
     * 初始化默认白名单
     */
    private void initializeDefaultWhitelist() {
        // 搜索引擎
        addDomain("google.com");
        addDomain("google.cn");
        addDomain("bing.com");
        addDomain("baidu.com");
        addDomain("yahoo.com");
        addDomain("yandex.com");
        addDomain("duckduckgo.com");
        
        // 代码托管平台
        addDomain("github.com");
        addDomain("gitlab.com");
        addDomain("bitbucket.org");
        addDomain("gitee.com");
        addDomain("coding.net");
        
        // 社交媒体
        addDomain("facebook.com");
        addDomain("twitter.com");
        addDomain("linkedin.com");
        addDomain("instagram.com");
        addDomain("weibo.com");
        addDomain("qq.com");
        addDomain("wechat.com");
        
        // 云服务商
        addDomain("amazonaws.com");
        addDomain("azure.com");
        addDomain("aliyun.com");
        addDomain("qcloud.com");
        addDomain("cloudflare.com");
        
        // 大型网站
        addDomain("youtube.com");
        addDomain("netflix.com");
        addDomain("amazon.com");
        addDomain("taobao.com");
        addDomain("tmall.com");
        addDomain("jd.com");
        addDomain("wikipedia.org");
        
        // CDN和静态资源
        addDomain("jsdelivr.net");
        addDomain("unpkg.com");
        addDomain("cdnjs.cloudflare.com");
        addDomain("ajax.googleapis.com");
        addDomain("fonts.googleapis.com");
        addDomain("fonts.gstatic.com");
        
        // 广告和分析
        addDomain("doubleclick.net");
        addDomain("googleadservices.com");
        addDomain("googlesyndication.com");
        addDomain("google-analytics.com");
        addDomain("googletagmanager.com");
        addDomain("facebook.net");
        
        // 其他知名服务
        addDomain("stackoverflow.com");
        addDomain("reddit.com");
        addDomain("medium.com");
        addDomain("zhihu.com");
        addDomain("csdn.net");
        addDomain("jianshu.com");
    }
    
    /**
     * 添加域名到白名单
     */
    public void addDomain(String domain) {
        if (domain != null && !domain.trim().isEmpty()) {
            whitelistedDomains.add(domain.toLowerCase().trim());
        }
    }
    
    /**
     * 从白名单移除域名
     */
    public void removeDomain(String domain) {
        if (domain != null) {
            whitelistedDomains.remove(domain.toLowerCase().trim());
        }
    }
    
    /**
     * 检查域名是否在白名单中
     */
    public boolean isWhitelisted(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }
        
        String lowerDomain = domain.toLowerCase().trim();
        
        // 直接匹配
        if (whitelistedDomains.contains(lowerDomain)) {
            return true;
        }
        
        // 检查子域名匹配
        for (String whitelistedDomain : whitelistedDomains) {
            if (lowerDomain.endsWith("." + whitelistedDomain)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查URL是否在白名单中
     */
    public boolean isUrlWhitelisted(String url) {
        try {
            java.net.URL urlObj = new java.net.URL(url);
            return isWhitelisted(urlObj.getHost());
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取所有白名单域名
     */
    public Set<String> getAllWhitelistedDomains() {
        return new java.util.HashSet<>(whitelistedDomains);
    }
    
    /**
     * 获取白名单域名数量
     */
    public int getWhitelistSize() {
        return whitelistedDomains.size();
    }
    
    /**
     * 清空白名单
     */
    public void clearWhitelist() {
        whitelistedDomains.clear();
    }
    
    /**
     * 重置为默认白名单
     */
    public void resetToDefault() {
        clearWhitelist();
        initializeDefaultWhitelist();
    }
    
    /**
     * 批量添加域名
     */
    public void addDomains(String[] domains) {
        if (domains != null) {
            for (String domain : domains) {
                addDomain(domain);
            }
        }
    }
    
    /**
     * 从字符串添加域名（逗号分隔）
     */
    public void addDomainsFromString(String domainsString) {
        if (domainsString != null && !domainsString.trim().isEmpty()) {
            String[] domains = domainsString.split("[,;\\s]+");
            addDomains(domains);
        }
    }
    
    /**
     * 导出白名单为字符串
     */
    public String exportWhitelistAsString() {
        return String.join(", ", whitelistedDomains);
    }
}
