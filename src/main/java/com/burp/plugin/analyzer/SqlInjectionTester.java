package com.burp.plugin.analyzer;

import burp.*;
import com.burp.plugin.core.VulnerabilityManager;
import com.burp.plugin.model.Vulnerability;
import com.burp.plugin.model.VulnerabilityType;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * SQL注入测试器 - 检测SQL注入漏洞
 */
public class SqlInjectionTester {
    
    private final IBurpExtenderCallbacks callbacks;
    private final IExtensionHelpers helpers;
    private final VulnerabilityManager vulnerabilityManager;
    
    // SQL错误模式
    private final List<Pattern> sqlErrorPatterns;
    
    // SQL注入测试载荷
    private final String[] sqlPayloads = {
        "'", "\"", "')", "\")", "';", "\";", "' OR '1'='1", "\" OR \"1\"=\"1",
        "' OR 1=1--", "\" OR 1=1--", "' UNION SELECT NULL--", "\" UNION SELECT NULL--",
        "'; DROP TABLE users--", "\"; DROP TABLE users--", "' AND 1=2--", "\" AND 1=2--"
    };
    
    public SqlInjectionTester(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers, 
                             VulnerabilityManager vulnerabilityManager) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.vulnerabilityManager = vulnerabilityManager;
        
        // 初始化SQL错误模式
        this.sqlErrorPatterns = initializeSqlErrorPatterns();
    }
    
    /**
     * 初始化SQL错误模式
     */
    private List<Pattern> initializeSqlErrorPatterns() {
        String[] errorPatterns = {
            // MySQL
            "You have an error in your SQL syntax",
            "mysql_fetch_array\\(\\)",
            "mysql_fetch_assoc\\(\\)",
            "mysql_fetch_row\\(\\)",
            "mysql_num_rows\\(\\)",
            "Warning.*mysql_.*",
            "MySQL server version for the right syntax",
            
            // PostgreSQL
            "PostgreSQL.*ERROR",
            "Warning.*\\Wpg_.*",
            "valid PostgreSQL result",
            "Npgsql\\.",
            
            // Microsoft SQL Server
            "Driver.* SQL[\\-\\_\\ ]*Server",
            "OLE DB.* SQL Server",
            "\\bSQL Server.*Driver",
            "Warning.*mssql_.*",
            "\\bSQL Server.*[0-9a-fA-F]{8}",
            "Exception.*\\WSystem\\.Data\\.SqlClient\\.",
            "Exception.*\\WRoadhouse\\.Cms\\.",
            "Microsoft SQL Native Client error '[0-9a-fA-F]{8}",
            "\\[SQL Server\\]",
            "ODBC SQL Server Driver",
            "ODBC Driver .* for SQL Server",
            "SQLServer JDBC Driver",
            "SqlException",
            
            // Oracle
            "\\bORA-[0-9][0-9][0-9][0-9]",
            "Oracle error",
            "Oracle.*Driver",
            "Warning.*\\Woci_.*",
            "Warning.*\\Wora_.*",
            
            // IBM DB2
            "CLI Driver.*DB2",
            "DB2 SQL error",
            "\\bdb2_\\w+\\(",
            
            // SQLite
            "SQLite/JDBCDriver",
            "SQLite.Exception",
            "System.Data.SQLite.SQLiteException",
            "Warning.*sqlite_.*",
            "Warning.*SQLite3::",
            "\\[SQLITE_ERROR\\]",
            
            // Generic SQL errors
            "SQL syntax.*MySQL",
            "Warning.*mysql_.*",
            "valid MySQL result",
            "MySqlClient\\.",
            "com\\.mysql\\.jdbc",
            "Zend_Db_(Adapter|Statement)_Mysql_Exception",
            "Pdo[./_\\\\]Mysql",
            "MySqlException",
            "SQLSTATE\\[[0-9]+\\]",
            "\\bSQL state \\[",
            "\\bInvalid query\\b",
            "\\bQuoted string not properly terminated\\b",
            "\\bUnclosed quotation mark after the character string\\b",
            "\\bSQL command not properly ended\\b"
        };
        
        List<Pattern> patterns = new ArrayList<>();
        for (String pattern : errorPatterns) {
            patterns.add(Pattern.compile(pattern, Pattern.CASE_INSENSITIVE));
        }
        return patterns;
    }
    
    /**
     * 测试参数是否存在SQL注入漏洞
     */
    public void testParameter(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        for (String payload : sqlPayloads) {
            try {
                // 创建测试请求
                IHttpRequestResponse testMessage = createTestRequest(originalMessage, paramName, payload);
                if (testMessage == null) {
                    continue;
                }
                
                // 发送请求
                IHttpRequestResponse response = callbacks.makeHttpRequest(
                    originalMessage.getHttpService(), testMessage.getRequest());
                
                if (response.getResponse() != null) {
                    // 分析响应
                    if (analyzeSqlInjectionResponse(response, payload)) {
                        // 发现SQL注入漏洞
                        reportSqlInjection(originalMessage, paramName, payload, response);
                        break; // 找到一个就够了，避免重复报告
                    }
                }
                
                // 添加延迟避免过于频繁的请求
                Thread.sleep(100);
                
            } catch (Exception e) {
                callbacks.printError("Error testing SQL injection for parameter " + paramName + ": " + e.getMessage());
            }
        }
    }
    
    /**
     * 创建测试请求
     */
    private IHttpRequestResponse createTestRequest(IHttpRequestResponse originalMessage, 
                                                  String paramName, String payload) {
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(originalMessage);
            List<IParameter> parameters = requestInfo.getParameters();
            
            // 查找目标参数
            IParameter targetParam = null;
            for (IParameter param : parameters) {
                if (param.getName().equals(paramName)) {
                    targetParam = param;
                    break;
                }
            }
            
            if (targetParam == null) {
                return null;
            }
            
            // 创建新的参数值
            String newValue = targetParam.getValue() + payload;
            IParameter newParam = helpers.buildParameter(targetParam.getName(), newValue, targetParam.getType());
            
            // 更新请求
            byte[] newRequest = helpers.updateParameter(originalMessage.getRequest(), newParam);
            
            // 创建新的请求响应对象
            return new IHttpRequestResponse() {
                @Override
                public byte[] getRequest() { return newRequest; }
                @Override
                public void setRequest(byte[] message) {}
                @Override
                public byte[] getResponse() { return null; }
                @Override
                public void setResponse(byte[] message) {}
                @Override
                public String getComment() { return null; }
                @Override
                public void setComment(String comment) {}
                @Override
                public String getHighlight() { return null; }
                @Override
                public void setHighlight(String color) {}
                @Override
                public IHttpService getHttpService() { return originalMessage.getHttpService(); }
                @Override
                public void setHttpService(IHttpService httpService) {}
            };
            
        } catch (Exception e) {
            callbacks.printError("Error creating test request: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 分析SQL注入响应
     */
    private boolean analyzeSqlInjectionResponse(IHttpRequestResponse response, String payload) {
        String responseString = helpers.bytesToString(response.getResponse());
        IResponseInfo responseInfo = helpers.analyzeResponse(response.getResponse());
        
        // 检查HTTP状态码
        short statusCode = responseInfo.getStatusCode();
        if (statusCode == 500 || statusCode == 400) {
            // 服务器错误可能表示SQL注入
            if (containsSqlError(responseString)) {
                return true;
            }
        }
        
        // 检查响应中是否包含SQL错误信息
        return containsSqlError(responseString);
    }
    
    /**
     * 检查响应是否包含SQL错误
     */
    private boolean containsSqlError(String response) {
        for (Pattern pattern : sqlErrorPatterns) {
            if (pattern.matcher(response).find()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 报告SQL注入漏洞
     */
    private void reportSqlInjection(IHttpRequestResponse originalMessage, String paramName, 
                                   String payload, IHttpRequestResponse testResponse) {
        IRequestInfo requestInfo = helpers.analyzeRequest(originalMessage);
        String url = requestInfo.getUrl().toString();
        String method = requestInfo.getMethod();
        
        String responseString = helpers.bytesToString(testResponse.getResponse());
        String evidence = extractSqlErrorEvidence(responseString);
        
        Vulnerability vulnerability = new Vulnerability.Builder()
                .type(VulnerabilityType.SQL_INJECTION)
                .url(url)
                .parameter(paramName)
                .payload(payload)
                .evidence(evidence)
                .severity(Vulnerability.Severity.HIGH)
                .method(method)
                .originalRequest(helpers.bytesToString(originalMessage.getRequest()))
                .response(responseString.length() > 2000 ? responseString.substring(0, 2000) + "..." : responseString)
                .build();
        
        vulnerabilityManager.addVulnerability(vulnerability);
        
        callbacks.printOutput("SQL Injection detected: " + url + " (parameter: " + paramName + ")");
    }
    
    /**
     * 提取SQL错误证据
     */
    private String extractSqlErrorEvidence(String response) {
        for (Pattern pattern : sqlErrorPatterns) {
            java.util.regex.Matcher matcher = pattern.matcher(response);
            if (matcher.find()) {
                // 返回匹配的错误信息及其上下文
                int start = Math.max(0, matcher.start() - 50);
                int end = Math.min(response.length(), matcher.end() + 50);
                return "SQL Error: " + response.substring(start, end).trim();
            }
        }
        return "SQL injection detected based on error response";
    }
}
