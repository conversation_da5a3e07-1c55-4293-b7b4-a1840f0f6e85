package com.burp.plugin.model;

/**
 * 漏洞类型枚举
 */
public enum VulnerabilityType {
    
    SQL_INJECTION("SQL Injection", "SQLi"),
    COMMAND_INJECTION("Command Injection", "CMDi"),
    PATH_TRAVERSAL("Path Traversal", "PT"),
    XSS("Cross-Site Scripting", "XSS"),
    PARAMETER_POLLUTION("Parameter Pollution", "PP"),
    SENSITIVE_DATA_EXPOSURE("Sensitive Data Exposure", "SDE");
    
    private final String displayName;
    private final String shortName;
    
    VulnerabilityType(String displayName, String shortName) {
        this.displayName = displayName;
        this.shortName = shortName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getShortName() {
        return shortName;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
