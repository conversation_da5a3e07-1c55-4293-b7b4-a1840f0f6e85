package com.burp.plugin.ui;

import com.burp.plugin.ParameterSecurityScanner;
import com.burp.plugin.core.VulnerabilityManager;
import com.burp.plugin.model.Vulnerability;
import com.burp.plugin.model.VulnerabilityType;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableRowSorter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 插件主界面
 */
public class MainPanel extends JPanel implements VulnerabilityManager.VulnerabilityListener {
    
    private final ParameterSecurityScanner plugin;
    private final VulnerabilityManager vulnerabilityManager;
    
    // UI组件
    private JTable vulnerabilityTable;
    private DefaultTableModel tableModel;
    private JLabel statsLabel;
    private JTextArea detailsArea;
    private JCheckBox enabledCheckBox;
    private JCheckBox sqlInjectionCheckBox;
    private JCheckBox apiFuzzingCheckBox;
    private JCheckBox crawlerCheckBox;
    
    // 统计标签
    private JLabel totalLabel;
    private JLabel highLabel;
    private JLabel mediumLabel;
    private JLabel lowLabel;
    
    public MainPanel(ParameterSecurityScanner plugin, VulnerabilityManager vulnerabilityManager) {
        this.plugin = plugin;
        this.vulnerabilityManager = vulnerabilityManager;
        
        // 注册为漏洞监听器
        vulnerabilityManager.addVulnerabilityListener(this);
        
        initializeUI();
        updateStats();
    }
    
    /**
     * 初始化UI界面
     */
    private void initializeUI() {
        setLayout(new BorderLayout());
        
        // 创建顶部控制面板
        JPanel controlPanel = createControlPanel();
        add(controlPanel, BorderLayout.NORTH);
        
        // 创建中间的分割面板
        JSplitPane splitPane = createSplitPane();
        add(splitPane, BorderLayout.CENTER);
        
        // 创建底部状态面板
        JPanel statusPanel = createStatusPanel();
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建控制面板
     */
    private JPanel createControlPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        panel.setBorder(BorderFactory.createTitledBorder("Plugin Controls"));
        
        // 插件开关
        enabledCheckBox = new JCheckBox("Enable Plugin", plugin.isEnabled());
        enabledCheckBox.addActionListener(e -> plugin.setEnabled(enabledCheckBox.isSelected()));
        panel.add(enabledCheckBox);
        
        panel.add(new JSeparator(SwingConstants.VERTICAL));
        
        // 功能开关
        sqlInjectionCheckBox = new JCheckBox("SQL Injection Testing", plugin.isSqlInjectionTestEnabled());
        sqlInjectionCheckBox.addActionListener(e -> plugin.setSqlInjectionTestEnabled(sqlInjectionCheckBox.isSelected()));
        panel.add(sqlInjectionCheckBox);
        
        apiFuzzingCheckBox = new JCheckBox("API Fuzzing", plugin.isApiFuzzingEnabled());
        apiFuzzingCheckBox.addActionListener(e -> plugin.setApiFuzzingEnabled(apiFuzzingCheckBox.isSelected()));
        panel.add(apiFuzzingCheckBox);
        
        crawlerCheckBox = new JCheckBox("Crawler", plugin.isCrawlerEnabled());
        crawlerCheckBox.addActionListener(e -> plugin.setCrawlerEnabled(crawlerCheckBox.isSelected()));
        panel.add(crawlerCheckBox);
        
        panel.add(new JSeparator(SwingConstants.VERTICAL));
        
        // 操作按钮
        JButton clearButton = new JButton("Clear All");
        clearButton.addActionListener(e -> {
            int result = JOptionPane.showConfirmDialog(this, 
                "Are you sure you want to clear all vulnerabilities?", 
                "Confirm Clear", JOptionPane.YES_NO_OPTION);
            if (result == JOptionPane.YES_OPTION) {
                vulnerabilityManager.clearAll();
            }
        });
        panel.add(clearButton);
        
        JButton exportButton = new JButton("Export Report");
        exportButton.addActionListener(e -> exportReport());
        panel.add(exportButton);
        
        return panel;
    }
    
    /**
     * 创建分割面板
     */
    private JSplitPane createSplitPane() {
        // 创建漏洞表格
        JPanel tablePanel = createTablePanel();
        
        // 创建详情面板
        JPanel detailsPanel = createDetailsPanel();
        
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT, tablePanel, detailsPanel);
        splitPane.setDividerLocation(300);
        splitPane.setResizeWeight(0.7);
        
        return splitPane;
    }
    
    /**
     * 创建表格面板
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Discovered Vulnerabilities"));
        
        // 创建表格模型
        String[] columnNames = {"Type", "Severity", "URL", "Parameter", "Time"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        vulnerabilityTable = new JTable(tableModel);
        vulnerabilityTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        vulnerabilityTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                showVulnerabilityDetails();
            }
        });
        
        // 设置表格排序
        TableRowSorter<DefaultTableModel> sorter = new TableRowSorter<>(tableModel);
        vulnerabilityTable.setRowSorter(sorter);
        
        // 设置列宽
        vulnerabilityTable.getColumnModel().getColumn(0).setPreferredWidth(120);
        vulnerabilityTable.getColumnModel().getColumn(1).setPreferredWidth(80);
        vulnerabilityTable.getColumnModel().getColumn(2).setPreferredWidth(300);
        vulnerabilityTable.getColumnModel().getColumn(3).setPreferredWidth(100);
        vulnerabilityTable.getColumnModel().getColumn(4).setPreferredWidth(120);
        
        JScrollPane scrollPane = new JScrollPane(vulnerabilityTable);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建详情面板
     */
    private JPanel createDetailsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("Vulnerability Details"));
        
        detailsArea = new JTextArea();
        detailsArea.setEditable(false);
        detailsArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        detailsArea.setText("Select a vulnerability to view details...");
        
        JScrollPane scrollPane = new JScrollPane(detailsArea);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建状态面板
     */
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        panel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        totalLabel = new JLabel("Total: 0");
        highLabel = new JLabel("High: 0");
        mediumLabel = new JLabel("Medium: 0");
        lowLabel = new JLabel("Low: 0");
        
        panel.add(totalLabel);
        panel.add(new JSeparator(SwingConstants.VERTICAL));
        panel.add(highLabel);
        panel.add(new JSeparator(SwingConstants.VERTICAL));
        panel.add(mediumLabel);
        panel.add(new JSeparator(SwingConstants.VERTICAL));
        panel.add(lowLabel);
        
        return panel;
    }
    
    /**
     * 显示漏洞详情
     */
    private void showVulnerabilityDetails() {
        int selectedRow = vulnerabilityTable.getSelectedRow();
        if (selectedRow == -1) {
            detailsArea.setText("Select a vulnerability to view details...");
            return;
        }
        
        // 获取实际行索引（考虑排序）
        int modelRow = vulnerabilityTable.convertRowIndexToModel(selectedRow);
        List<Vulnerability> vulnerabilities = vulnerabilityManager.getAllVulnerabilities();
        
        if (modelRow < vulnerabilities.size()) {
            Vulnerability vuln = vulnerabilities.get(modelRow);
            displayVulnerabilityDetails(vuln);
        }
    }
    
    /**
     * 显示漏洞详细信息
     */
    private void displayVulnerabilityDetails(Vulnerability vuln) {
        StringBuilder details = new StringBuilder();
        details.append("=== VULNERABILITY DETAILS ===\n\n");
        details.append("ID: ").append(vuln.getId()).append("\n");
        details.append("Type: ").append(vuln.getType().getDisplayName()).append("\n");
        details.append("Severity: ").append(vuln.getSeverity().getDisplayName()).append("\n");
        details.append("URL: ").append(vuln.getUrl()).append("\n");
        details.append("Parameter: ").append(vuln.getParameter()).append("\n");
        details.append("Method: ").append(vuln.getMethod()).append("\n");
        details.append("Discovered: ").append(vuln.getFormattedDiscoveryTime()).append("\n\n");
        
        if (vuln.getPayload() != null) {
            details.append("Payload: ").append(vuln.getPayload()).append("\n\n");
        }
        
        details.append("Description:\n").append(vuln.getDescription()).append("\n\n");
        details.append("Evidence:\n").append(vuln.getEvidence()).append("\n\n");
        details.append("Recommendation:\n").append(vuln.getRecommendation()).append("\n\n");
        
        if (vuln.getOriginalRequest() != null) {
            details.append("=== ORIGINAL REQUEST ===\n");
            details.append(vuln.getOriginalRequest()).append("\n\n");
        }
        
        if (vuln.getResponse() != null) {
            details.append("=== RESPONSE ===\n");
            details.append(vuln.getResponse()).append("\n");
        }
        
        detailsArea.setText(details.toString());
        detailsArea.setCaretPosition(0);
    }
    
    /**
     * 更新统计信息
     */
    private void updateStats() {
        SwingUtilities.invokeLater(() -> {
            int total = vulnerabilityManager.getTotalCount();
            int high = vulnerabilityManager.getHighRiskCount();
            int medium = vulnerabilityManager.getMediumRiskCount();
            int low = vulnerabilityManager.getLowRiskCount();
            
            totalLabel.setText("Total: " + total);
            highLabel.setText("High: " + high);
            mediumLabel.setText("Medium: " + medium);
            lowLabel.setText("Low: " + low);
            
            // 设置颜色
            highLabel.setForeground(high > 0 ? Color.RED : Color.BLACK);
            mediumLabel.setForeground(medium > 0 ? Color.ORANGE : Color.BLACK);
            lowLabel.setForeground(low > 0 ? Color.BLUE : Color.BLACK);
        });
    }
    
    /**
     * 导出报告
     */
    private void exportReport() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("Export Vulnerability Report");
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("HTML files", "html"));
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String timestamp = sdf.format(new Date());
        fileChooser.setSelectedFile(new File("vulnerability_report_" + timestamp + ".html"));
        
        if (fileChooser.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
            File file = fileChooser.getSelectedFile();
            try {
                generateHtmlReport(file);
                JOptionPane.showMessageDialog(this, "Report exported successfully to: " + file.getAbsolutePath());
            } catch (IOException e) {
                JOptionPane.showMessageDialog(this, "Error exporting report: " + e.getMessage(), 
                    "Export Error", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * 生成HTML报告
     */
    private void generateHtmlReport(File file) throws IOException {
        List<Vulnerability> vulnerabilities = vulnerabilityManager.getAllVulnerabilities();
        Map<VulnerabilityType, Integer> stats = vulnerabilityManager.getVulnerabilityStats();
        
        try (FileWriter writer = new FileWriter(file)) {
            writer.write("<!DOCTYPE html>\n<html>\n<head>\n");
            writer.write("<title>Parameter Security Scanner Report</title>\n");
            writer.write("<style>\n");
            writer.write("body { font-family: Arial, sans-serif; margin: 20px; }\n");
            writer.write("h1, h2 { color: #333; }\n");
            writer.write("table { border-collapse: collapse; width: 100%; margin: 20px 0; }\n");
            writer.write("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n");
            writer.write("th { background-color: #f2f2f2; }\n");
            writer.write(".high { color: red; font-weight: bold; }\n");
            writer.write(".medium { color: orange; font-weight: bold; }\n");
            writer.write(".low { color: blue; }\n");
            writer.write(".details { margin: 10px 0; padding: 10px; background-color: #f9f9f9; }\n");
            writer.write("</style>\n</head>\n<body>\n");
            
            writer.write("<h1>Parameter Security Scanner Report</h1>\n");
            writer.write("<p>Generated on: " + new Date() + "</p>\n");
            
            // 统计信息
            writer.write("<h2>Summary</h2>\n");
            writer.write("<p>Total vulnerabilities found: " + vulnerabilities.size() + "</p>\n");
            writer.write("<ul>\n");
            for (Map.Entry<VulnerabilityType, Integer> entry : stats.entrySet()) {
                if (entry.getValue() > 0) {
                    writer.write("<li>" + entry.getKey().getDisplayName() + ": " + entry.getValue() + "</li>\n");
                }
            }
            writer.write("</ul>\n");
            
            // 漏洞详情
            writer.write("<h2>Vulnerability Details</h2>\n");
            writer.write("<table>\n");
            writer.write("<tr><th>Type</th><th>Severity</th><th>URL</th><th>Parameter</th><th>Time</th></tr>\n");
            
            for (Vulnerability vuln : vulnerabilities) {
                String severityClass = vuln.getSeverity().name().toLowerCase();
                writer.write("<tr>\n");
                writer.write("<td>" + vuln.getType().getDisplayName() + "</td>\n");
                writer.write("<td class=\"" + severityClass + "\">" + vuln.getSeverity().getDisplayName() + "</td>\n");
                writer.write("<td>" + escapeHtml(vuln.getUrl()) + "</td>\n");
                writer.write("<td>" + escapeHtml(vuln.getParameter()) + "</td>\n");
                writer.write("<td>" + vuln.getFormattedDiscoveryTime() + "</td>\n");
                writer.write("</tr>\n");
                
                // 详细信息
                writer.write("<tr><td colspan=\"5\">\n");
                writer.write("<div class=\"details\">\n");
                writer.write("<strong>Description:</strong> " + escapeHtml(vuln.getDescription()) + "<br>\n");
                writer.write("<strong>Evidence:</strong> " + escapeHtml(vuln.getEvidence()) + "<br>\n");
                writer.write("<strong>Recommendation:</strong> " + escapeHtml(vuln.getRecommendation()) + "\n");
                writer.write("</div>\n");
                writer.write("</td></tr>\n");
            }
            
            writer.write("</table>\n");
            writer.write("</body>\n</html>");
        }
    }
    
    /**
     * HTML转义
     */
    private String escapeHtml(String text) {
        if (text == null) return "";
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#x27;");
    }
    
    @Override
    public void onVulnerabilityFound(Vulnerability vulnerability) {
        SwingUtilities.invokeLater(() -> {
            // 添加到表格
            Object[] row = {
                vulnerability.getType().getDisplayName(),
                vulnerability.getSeverity().getDisplayName(),
                vulnerability.getUrl(),
                vulnerability.getParameter(),
                vulnerability.getFormattedDiscoveryTime()
            };
            tableModel.addRow(row);
            
            // 更新统计
            updateStats();
        });
    }
    
    @Override
    public void onVulnerabilitiesCleared() {
        SwingUtilities.invokeLater(() -> {
            tableModel.setRowCount(0);
            detailsArea.setText("Select a vulnerability to view details...");
            updateStats();
        });
    }
}
