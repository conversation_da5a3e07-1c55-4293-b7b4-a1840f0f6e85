package com.burp.plugin;

import burp.*;
import com.burp.plugin.ui.MainPanel;
import com.burp.plugin.core.HttpListener;
import com.burp.plugin.core.VulnerabilityManager;
import com.burp.plugin.core.CrawlerManager;
import com.burp.plugin.core.DomainWhitelistManager;

import javax.swing.*;
import java.awt.*;
import java.io.PrintWriter;

/**
 * Burp Suite插件主类 - 参数安全扫描器
 * 功能包括：参数检测、SQL注入测试、API模糊测试、同域爬虫
 */
public class ParameterSecurityScanner implements IBurpExtender, ITab, IHttpListener {
    
    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private PrintWriter stdout;
    private PrintWriter stderr;
    
    // UI组件
    private MainPanel mainPanel;
    
    // 核心功能模块
    private HttpListener httpListener;
    private VulnerabilityManager vulnerabilityManager;
    private CrawlerManager crawlerManager;
    private DomainWhitelistManager domainWhitelistManager;
    
    // 插件配置
    private boolean isEnabled = true;
    private boolean sqlInjectionTestEnabled = true;
    private boolean apiFuzzingEnabled = true;
    private boolean crawlerEnabled = false; // 默认关闭爬虫
    
    @Override
    public void registerExtenderCallbacks(IBurpExtenderCallbacks callbacks) {
        this.callbacks = callbacks;
        this.helpers = callbacks.getHelpers();
        this.stdout = new PrintWriter(callbacks.getStdout(), true);
        this.stderr = new PrintWriter(callbacks.getStderr(), true);
        
        // 设置插件名称
        callbacks.setExtensionName("Parameter Security Scanner");
        
        // 初始化核心模块
        initializeModules();
        
        // 注册HTTP监听器
        callbacks.registerHttpListener(this);

        // 创建UI界面并添加标签页
        initializeUI();
        
        stdout.println("参数安全扫描器加载成功！");
        stdout.println("功能包括: 参数检测、SQL注入测试、API模糊测试、同域爬虫");
    }
    
    /**
     * 初始化核心功能模块
     */
    private void initializeModules() {
        vulnerabilityManager = new VulnerabilityManager();
        domainWhitelistManager = new DomainWhitelistManager();
        crawlerManager = new CrawlerManager(callbacks, helpers, vulnerabilityManager);
        httpListener = new HttpListener(callbacks, helpers, vulnerabilityManager, crawlerManager, domainWhitelistManager);

        stdout.println("域名白名单已加载，包含 " + domainWhitelistManager.getWhitelistSize() + " 个域名");
    }
    
    /**
     * 初始化UI界面
     */
    private void initializeUI() {
        try {
            if (SwingUtilities.isEventDispatchThread()) {
                // 如果已经在EDT线程中，直接创建
                createUI();
            } else {
                // 否则在EDT线程中创建
                SwingUtilities.invokeAndWait(() -> createUI());
            }

            // 添加插件标签页
            callbacks.addSuiteTab(this);

        } catch (Exception e) {
            stderr.println("Error initializing UI: " + e.getMessage());
            e.printStackTrace(stderr);
        }
    }

    /**
     * 创建UI组件
     */
    private void createUI() {
        mainPanel = new MainPanel(this, vulnerabilityManager);
    }
    
    @Override
    public String getTabCaption() {
        return "参数扫描器";
    }
    
    @Override
    public Component getUiComponent() {
        if (mainPanel == null) {
            // 如果UI还没有初始化，创建一个临时面板
            JPanel tempPanel = new JPanel();
            tempPanel.add(new JLabel("Loading Parameter Security Scanner..."));
            return tempPanel;
        }
        return mainPanel;
    }
    
    @Override
    public void processHttpMessage(int toolFlag, boolean messageIsRequest, IHttpRequestResponse messageInfo) {
        // 只处理Proxy工具的消息
        if (toolFlag == IBurpExtenderCallbacks.TOOL_PROXY && !messageIsRequest && isEnabled) {
            httpListener.processHttpMessage(toolFlag, messageIsRequest, messageInfo);
        }
    }
    
    // Getter和Setter方法
    public boolean isEnabled() {
        return isEnabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.isEnabled = enabled;
        stdout.println("插件已" + (enabled ? "启用" : "禁用"));
    }

    public boolean isSqlInjectionTestEnabled() {
        return sqlInjectionTestEnabled;
    }

    public void setSqlInjectionTestEnabled(boolean enabled) {
        this.sqlInjectionTestEnabled = enabled;
        httpListener.setSqlInjectionTestEnabled(enabled);
        stdout.println("SQL注入检测已" + (enabled ? "启用" : "禁用"));
    }

    public boolean isApiFuzzingEnabled() {
        return apiFuzzingEnabled;
    }

    public void setApiFuzzingEnabled(boolean enabled) {
        this.apiFuzzingEnabled = enabled;
        httpListener.setApiFuzzingEnabled(enabled);
        stdout.println("API模糊测试已" + (enabled ? "启用" : "禁用"));
    }

    public boolean isCrawlerEnabled() {
        return crawlerEnabled;
    }

    public void setCrawlerEnabled(boolean enabled) {
        this.crawlerEnabled = enabled;
        httpListener.setCrawlerEnabled(enabled);
        stdout.println("同域爬虫已" + (enabled ? "启用" : "禁用"));
    }
    
    public IBurpExtenderCallbacks getCallbacks() {
        return callbacks;
    }
    
    public IExtensionHelpers getHelpers() {
        return helpers;
    }
    
    public PrintWriter getStdout() {
        return stdout;
    }
    
    public PrintWriter getStderr() {
        return stderr;
    }
    
    public VulnerabilityManager getVulnerabilityManager() {
        return vulnerabilityManager;
    }
    
    public CrawlerManager getCrawlerManager() {
        return crawlerManager;
    }

    public DomainWhitelistManager getDomainWhitelistManager() {
        return domainWhitelistManager;
    }
}
