# Parameter Security Scanner - Burp Suite插件

一个功能强大的Burp Suite插件，专门用于检测Web应用程序中的参数安全漏洞。

## 功能特性

### 🔍 参数检测与分析
- **全面参数提取**: 自动提取URL参数、POST参数、Cookie参数和自定义Header参数
- **多格式支持**: 支持表单数据、JSON、XML、多部分表单等多种数据格式
- **参数污染检测**: 识别同名参数的重复提交问题

### 💉 SQL注入检测
- **智能载荷测试**: 使用多种SQL注入载荷进行自动化测试
- **错误模式识别**: 支持MySQL、PostgreSQL、SQL Server、Oracle、SQLite等主流数据库的错误模式
- **误报减少**: 通过多重验证机制减少误报率

### 🔧 API模糊测试
- **命令注入检测**: 测试系统命令执行漏洞
- **路径遍历检测**: 识别目录遍历和文件包含漏洞
- **XSS检测**: 检测反射型跨站脚本攻击
- **多平台支持**: 支持Linux、Windows等不同操作系统环境

### 🕷️ 同域爬虫
- **受限制爬取**: 仅爬取同域名下的页面，避免越界
- **深度控制**: 可配置爬取深度，默认最大深度为2层
- **智能过滤**: 自动过滤静态资源和危险操作链接
- **URL数量限制**: 防止无限爬取，保护目标服务器

### 📊 报告与管理
- **实时监控**: 实时显示发现的漏洞信息
- **详细报告**: 提供漏洞详情、载荷、证据和修复建议
- **HTML导出**: 支持导出专业的HTML格式安全报告
- **统计分析**: 按严重程度统计漏洞分布

## 安装方法

### 1. 编译插件

```bash
# 克隆项目
git clone <repository-url>
cd burp-parameter-scanner

# 使用Maven编译
mvn clean package

# 编译完成后，JAR文件位于 target/parameter-security-scanner-1.0.0.jar
```

### 2. 在Burp Suite中安装

1. 打开Burp Suite
2. 进入 `Extender` -> `Extensions`
3. 点击 `Add`
4. 选择 `Java` 作为扩展类型
5. 选择编译好的JAR文件
6. 点击 `Next` 完成安装

## 使用指南

### 基本配置

1. **启用插件**: 在插件界面勾选 "Enable Plugin"
2. **功能选择**: 根据需要启用以下功能：
   - SQL Injection Testing: SQL注入检测
   - API Fuzzing: API模糊测试
   - Crawler: 同域爬虫

### 工作流程

1. **配置代理**: 确保浏览器流量通过Burp Suite代理
2. **浏览目标**: 正常浏览目标Web应用
3. **自动检测**: 插件会自动分析HTTP History中的请求
4. **查看结果**: 在插件界面查看发现的漏洞
5. **导出报告**: 使用"Export Report"功能生成HTML报告

### 高级配置

#### 爬虫设置
- **最大深度**: 1-5层，默认2层
- **最大URL数**: 10-200个，默认50个
- **爬取延迟**: 100ms-5s，默认1s

#### 安全考虑
- 插件默认关闭爬虫功能，需手动启用
- 自动过滤危险操作（logout、delete等）
- 限制爬取范围在同域名内

## 检测能力

### SQL注入
- 单引号注入
- 双引号注入
- 数字型注入
- UNION查询注入
- 布尔盲注
- 时间盲注

### 命令注入
- Linux命令注入
- Windows命令注入
- 命令分隔符绕过
- 反引号执行
- 变量替换

### 路径遍历
- 相对路径遍历
- 绝对路径访问
- URL编码绕过
- 双重编码绕过

### XSS检测
- 反射型XSS
- 事件处理器XSS
- JavaScript伪协议
- HTML标签注入

## 技术架构

### 核心模块
- **HttpListener**: HTTP请求监听和分析
- **ParameterAnalyzer**: 参数提取和解析
- **SqlInjectionTester**: SQL注入检测引擎
- **ApiFuzzer**: API安全测试引擎
- **CrawlerManager**: 爬虫管理器
- **VulnerabilityManager**: 漏洞管理和存储

### 设计特点
- **异步处理**: 使用线程池避免阻塞Burp Suite
- **内存优化**: 智能缓存和去重机制
- **错误处理**: 完善的异常处理和日志记录
- **扩展性**: 模块化设计，易于扩展新功能

## 注意事项

### 使用限制
- 仅用于授权的安全测试
- 不要在生产环境中使用爬虫功能
- 注意控制测试频率，避免对目标服务器造成压力

### 性能建议
- 大型应用建议关闭爬虫功能
- 可以选择性启用检测模块
- 定期清理漏洞记录释放内存

### 法律声明
- 本工具仅用于合法的安全测试
- 使用者需确保拥有目标系统的测试授权
- 作者不承担任何滥用责任

## 开发信息

- **开发语言**: Java 11+
- **依赖框架**: Burp Suite API, JSoup, Apache HttpClient
- **构建工具**: Maven
- **许可证**: MIT License

## 更新日志

### v1.0.0
- 初始版本发布
- 实现基础参数检测功能
- 支持SQL注入、命令注入、路径遍历、XSS检测
- 集成同域爬虫功能
- 提供HTML报告导出

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
