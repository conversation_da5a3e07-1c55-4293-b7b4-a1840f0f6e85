# 📁 春秋蝉3.0 域名合并功能说明

## 🎯 功能概述

域名合并功能将相同域名的漏洞合并显示为一个可展开的文件夹行，用户可以点击文件夹查看该域名下的所有漏洞详情。这样可以让漏洞列表更加整洁，便于按域名进行漏洞管理。

## ✨ 功能特点

### 1. 智能域名分组
- **自动识别**: 自动提取URL中的域名进行分组
- **容错处理**: 处理各种异常URL格式
- **完整覆盖**: 所有漏洞都会被正确分组，不会遗漏

### 2. 文件夹式显示
- **统一格式**: 所有域名都显示为文件夹行（不管有几个漏洞）
- **状态图标**: 📁 (折叠) / 📂 (展开)
- **统计信息**: 显示该域名下的漏洞数量和严重程度分布

### 3. 交互式展开/折叠
- **点击展开**: 点击域名行展开查看详细漏洞
- **点击折叠**: 再次点击折叠隐藏详细信息
- **状态记忆**: 记住每个域名的展开/折叠状态

## 🎨 显示效果

### 启用域名合并前 (传统列表)
```
┌─────────────────────────────────────────────────────────────┐
│ 漏洞类型          │ 严重程度 │ URL地址                      │
├─────────────────────────────────────────────────────────────┤
│ XSS跨站脚本攻击   │ 高危     │ http://example.com/page1.php │
│ SQL注入          │ 高危     │ http://example.com/page2.php │
│ 敏感信息泄露      │ 中危     │ http://example.com/api/user  │
│ XSS跨站脚本攻击   │ 中危     │ http://test.com/login.php    │
│ SQL注入          │ 高危     │ http://test.com/admin.php    │
└─────────────────────────────────────────────────────────────┘
```

### 启用域名合并后 (文件夹式)
```
┌─────────────────────────────────────────────────────────────┐
│ 漏洞类型          │ 严重程度    │ URL地址                    │
├─────────────────────────────────────────────────────────────┤
│ 📁 example.com (3个漏洞) │ 🔴2 🟡1 │ 点击展开查看详细信息 │ [点击展开] │
│ 📁 test.com (2个漏洞)    │ 🔴2     │ 点击展开查看详细信息 │ [点击展开] │
└─────────────────────────────────────────────────────────────┘
```

### 点击展开后
```
┌─────────────────────────────────────────────────────────────┐
│ 漏洞类型          │ 严重程度    │ URL地址                    │
├─────────────────────────────────────────────────────────────┤
│ 📂 example.com (3个漏洞) │ 🔴2 🟡1 │ 点击展开查看详细信息 │ [点击折叠] │
│   └─ XSS跨站脚本攻击     │ 高危     │ http://example.com/page1.php │
│   └─ SQL注入            │ 高危     │ http://example.com/page2.php │
│   └─ 敏感信息泄露        │ 中危     │ http://example.com/api/user  │
│ 📁 test.com (2个漏洞)    │ 🔴2     │ 点击展开查看详细信息 │ [点击展开] │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 使用方法

### 1. 启用域名合并
1. 在春秋蝉3.0界面中找到"高级功能"区域
2. 勾选 `📁 同域名合并` 复选框
3. 漏洞列表自动切换为域名分组显示

### 2. 查看域名统计
- **漏洞数量**: 显示该域名下的总漏洞数
- **严重程度分布**: 
  - 🟣 Critical (严重)
  - 🔴 High (高危)
  - 🟡 Medium (中危)  
  - 🟢 Low (低危)

### 3. 展开/折叠操作
- **展开**: 点击 📁 域名行，查看该域名下的所有漏洞
- **折叠**: 点击 📂 域名行，隐藏详细漏洞信息
- **状态保持**: 展开/折叠状态会被记住，直到刷新

### 4. 查看漏洞详情
- 展开域名后，点击具体的漏洞行查看详细信息
- 漏洞详情在右侧面板显示，支持黑色主题和高亮

## 🎯 适用场景

### 1. 多域名测试
- 测试多个子域名或不同域名的应用
- 快速了解每个域名的安全状况
- 按域名优先级处理漏洞

### 2. 大量漏洞管理
- 当发现大量漏洞时，按域名分组便于管理
- 避免漏洞列表过长难以查看
- 提高漏洞处理效率

### 3. 报告生成
- 按域名组织漏洞报告
- 清晰展示每个域名的安全问题
- 便于向不同团队分配修复任务

## ⚙️ 技术实现

### 1. 域名提取算法
```java
// 从URL中提取域名
try {
    URL url = new URL(vuln.getUrl());
    String domain = url.getHost();
    if (domain != null) {
        grouped.computeIfAbsent(domain, k -> new ArrayList<>()).add(vuln);
    }
} catch (Exception e) {
    // 容错处理：URL解析失败时使用原始URL
    String fallbackDomain = vuln.getUrl();
    if (fallbackDomain.length() > 50) {
        fallbackDomain = fallbackDomain.substring(0, 50) + "...";
    }
    grouped.computeIfAbsent(fallbackDomain, k -> new ArrayList<>()).add(vuln);
}
```

### 2. 严重程度统计
```java
// 统计各严重程度的漏洞数量
int criticalCount = 0, highCount = 0, mediumCount = 0, lowCount = 0;
for (Vulnerability vuln : domainVulns) {
    switch (vuln.getSeverity()) {
        case CRITICAL: criticalCount++; break;
        case HIGH: highCount++; break;
        case MEDIUM: mediumCount++; break;
        case LOW: lowCount++; break;
    }
}
```

### 3. 展开/折叠状态管理
```java
// 记住每个域名的展开状态
private final Map<String, Boolean> domainExpandedState = new HashMap<>();

// 切换状态
boolean currentState = domainExpandedState.getOrDefault(domain, false);
domainExpandedState.put(domain, !currentState);
```

## 🔍 与其他功能的配合

### 1. 搜索功能
- 域名合并状态下仍可使用搜索功能
- 搜索结果会自动展开相关域名
- 支持搜索域名、漏洞类型、URL等

### 2. 右键菜单
- 在域名行上右键可以操作该域名下的所有漏洞
- 在具体漏洞行上右键操作单个漏洞
- 支持批量删除、导出等操作

### 3. 漏洞详情
- 点击展开后的漏洞行查看详情
- 详情面板支持黑色主题和高亮显示
- 保持原有的所有详情功能

## 📊 性能优化

### 1. 内存管理
- 只在需要时进行域名分组
- 状态信息使用轻量级Map存储
- 避免重复计算和数据冗余

### 2. 界面响应
- 展开/折叠操作即时响应
- 大量漏洞时分页显示
- 异步处理避免界面卡顿

### 3. 数据一致性
- 确保分组数据与原始数据一致
- 实时更新统计信息
- 自动处理数据变化

## 🎉 总结

域名合并功能让春秋蝉3.0的漏洞管理更加智能和高效：

- ✅ **界面整洁**: 文件夹式显示，避免列表过长
- ✅ **操作便捷**: 点击展开/折叠，交互直观
- ✅ **信息丰富**: 显示统计信息和严重程度分布
- ✅ **功能完整**: 与搜索、详情、右键菜单完美配合
- ✅ **性能优秀**: 快速响应，内存优化

**域名合并功能让漏洞管理像管理文件夹一样简单！** 📁✨

---

**功能状态**: ✅ 已完成并测试  
**版本**: 3.0.0  
**更新时间**: 2025年8月5日 19:53
