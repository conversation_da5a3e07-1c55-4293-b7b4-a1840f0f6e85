# 📖 春秋蝉3.0 使用手册

## 🚀 快速开始

### 安装步骤

#### 1. 下载插件
- 获取 `chunqiu-chan-3.0.0.jar` 文件 (4.2MB)
- 确保文件完整性和安全性

#### 2. 加载到Burp Suite
1. 打开Burp Suite Professional或Community版本
2. 进入 **Extender** 标签页
3. 点击 **Extensions** 子标签
4. 点击 **Add** 按钮
5. 选择 **Extension type**: Java
6. 点击 **Select file** 选择 `chunqiu-chan-3.0.0.jar`
7. 点击 **Next** 完成加载

#### 3. 验证安装
- 在Extender中看到 "春秋蝉 3.0 (ChunQiu Chan 3.0)" 
- 状态显示为 "Loaded"
- 在主界面顶部出现 "春秋蝉 3.0" 标签页

## 🎯 界面介绍

### 主界面布局

```
┌─────────────────────────────────────────────────────────────┐
│                    春秋蝉 3.0 控制面板                        │
├─────────────────────────────────────────────────────────────┤
│ 第一行 - 基础控制                                            │
│ [🔌 启用插件]                                               │
├─────────────────────────────────────────────────────────────┤
│ 第二行 - 检测功能                                            │
│ [🛡️ SQL注入检测] [⚡ XSS检测] [🔍 API模糊测试]              │
│ [🕷️ 同域爬虫] [🔐 敏感信息检测]                             │
├─────────────────────────────────────────────────────────────┤
│ 第三行 - 高级功能                                            │
│ [📁 同域名合并] [⏱️ 实时监控] 间隔:[30]秒                   │
│ 🔍 搜索:[_____________] (0/0)                               │
├─────────────────────────────────────────────────────────────┤
│ 第四行 - 操作按钮                                            │
│ [🔄 刷新] [🗑️ 清空记录] [📊 导出报告]                       │
│ [🌐 域名白名单] [🔐 敏感信息规则]                            │
└─────────────────────────────────────────────────────────────┘
```

### 漏洞列表区域
- **表格显示**: 漏洞类型、严重程度、URL、参数、发现时间
- **颜色分级**: 🔴高危 🟡中危 🟢低危
- **排序功能**: 点击列标题进行排序
- **右键菜单**: 删除、复制、导出等操作

### 漏洞详情区域 (黑色主题)
- **高亮显示**: URL(青色)、载荷(红色)、参数(黄色)
- **分类信息**: 基本信息、URL信息、载荷信息、证据等
- **操作按钮**: 字体调整、复制功能

### 状态栏
- **统计信息**: 📊 总计、🔴 高危、🟡 中危、🟢 低危
- **运行状态**: 插件版本和运行状态

## 🔧 功能详解

### 1. 基础检测功能

#### SQL注入检测 🛡️
- **功能**: 自动检测SQL注入漏洞
- **使用方法**: 勾选"SQL注入检测"复选框
- **检测范围**: GET/POST参数、Cookie、Header
- **支持数据库**: MySQL、PostgreSQL、Oracle、SQL Server等

#### XSS检测 ⚡
- **功能**: 200个载荷的高级XSS检测
- **使用方法**: 勾选"XSS检测"复选框
- **检测类型**: 反射型、存储型、DOM型XSS
- **WAF绕过**: 支持Cloudflare、Akamai等主流WAF

#### API模糊测试 🔍
- **功能**: REST API安全测试
- **使用方法**: 勾选"API模糊测试"复选框
- **测试内容**: 参数污染、权限绕过、数据验证

#### 同域爬虫 🕷️
- **功能**: 自动发现同域名下的页面
- **使用方法**: 勾选"同域爬虫"复选框
- **爬取范围**: 当前域名下的所有可访问页面

#### 敏感信息检测 🔐
- **功能**: 自动识别敏感数据泄露
- **使用方法**: 勾选"敏感信息检测"复选框
- **检测内容**: 身份证、手机号、邮箱、API密钥等

### 2. 高级功能

#### 同域名合并 📁
- **功能**: 将相同域名的漏洞进行分组显示
- **使用方法**: 
  1. 勾选"同域名合并"复选框
  2. 点击域名行展开/折叠漏洞列表
- **显示效果**:
  ```
  📁 example.com (5个漏洞) [点击展开]
  📂 example.com (5个漏洞) [点击折叠]
    └─ XSS跨站脚本攻击
    └─ SQL注入
  ```

#### 实时监控 ⏱️
- **功能**: 对Burp历史数据包进行实时监控
- **使用方法**:
  1. 勾选"实时监控"复选框
  2. 设置刷新间隔(5-300秒)
  3. 查看监控结果
- **监控内容**: 敏感信息检测、新增数据包分析

#### 搜索功能 🔍
- **功能**: 全文搜索漏洞信息
- **使用方法**: 在搜索框中输入关键词
- **搜索范围**: URL、参数名、载荷、漏洞类型、证据
- **搜索特点**: 实时过滤、大小写不敏感、部分匹配

### 3. 操作功能

#### 刷新 🔄
- **功能**: 刷新漏洞列表显示
- **使用场景**: 更新显示状态、重新加载数据

#### 清空记录 🗑️
- **功能**: 清空所有漏洞记录
- **安全确认**: 需要用户确认操作
- **注意**: 此操作不可撤销

#### 导出报告 📊
- **功能**: 导出漏洞报告
- **支持格式**: TXT文本格式
- **报告内容**: 完整的漏洞详情和统计信息

#### 域名白名单 🌐
- **功能**: 设置不进行检测的域名
- **使用方法**: 点击按钮打开白名单配置对话框

#### 敏感信息规则 🔐
- **功能**: 自定义敏感信息检测规则
- **使用方法**: 点击按钮打开规则配置对话框

## 🎨 右键菜单功能

在漏洞列表中右键点击可以使用以下功能：

### 删除操作
- **删除这一条**: 删除选中的漏洞记录
- **安全确认**: 显示漏洞详情确认删除

### 复制操作
- **复制URL**: 复制漏洞的URL地址
- **复制参数名**: 复制漏洞的参数名称
- **复制载荷**: 复制攻击载荷内容

### 导出操作
- **提取所有URL**: 提取并显示所有唯一URL
- **导出此记录**: 导出单条漏洞的详细信息

## 📊 漏洞详情查看

### 黑色主题界面
- **背景色**: 黑色护眼设计
- **字体色**: 绿色主色调
- **高亮显示**: 关键信息彩色突出

### 信息分类
1. **基本信息**: 漏洞类型、严重程度、发现时间
2. **URL信息**: 目标URL地址(青色高亮)
3. **参数信息**: 参数名称(黄色高亮)
4. **攻击载荷**: 载荷内容(红色高亮)
5. **漏洞证据**: 证据信息(橙色背景)
6. **原始请求**: 完整的HTTP请求
7. **响应内容**: HTTP响应(截取前2000字符)

### 操作功能
- **字体调整**: 小、中、大、特大四种字体大小
- **复制功能**: 复制全部内容到剪贴板

## ⚙️ 配置建议

### 基础配置
```
推荐设置:
✅ 启用插件
✅ SQL注入检测
✅ XSS检测
✅ 敏感信息检测
✅ 同域名合并
❌ 同域爬虫 (根据需要)
❌ API模糊测试 (根据需要)
❌ 实时监控 (根据需要)
```

### 性能优化配置
- **实时监控间隔**: 建议30-60秒
- **同域爬虫**: 仅在需要时启用
- **API模糊测试**: 针对API应用启用

### 安全配置
- **域名白名单**: 添加内部系统域名
- **敏感信息规则**: 根据业务需求自定义

## 🔍 故障排除

### 常见问题

#### 1. 插件加载失败
- **检查**: Java版本是否为8+
- **解决**: 升级Java版本或使用兼容版本

#### 2. 检测功能无效
- **检查**: 是否勾选了相应的检测功能
- **解决**: 确保插件已启用且功能开关正确

#### 3. 界面显示异常
- **检查**: Burp Suite版本兼容性
- **解决**: 重新加载插件或重启Burp Suite

#### 4. 性能问题
- **检查**: 实时监控间隔设置
- **解决**: 增加监控间隔或关闭不必要功能

### 日志查看
- **位置**: Burp Suite的Extender -> Extensions -> 春秋蝉3.0 -> Output/Errors
- **内容**: 插件运行日志和错误信息

## 📞 技术支持

### 联系方式
- **开发团队**: Security Research Team
- **版本**: 3.0.0
- **发布日期**: 2025年8月5日

### 反馈渠道
- **功能建议**: 通过GitHub Issues提交
- **Bug报告**: 提供详细的复现步骤和日志
- **使用问题**: 查看文档或联系技术支持

---

**春秋蝉3.0 - 让安全测试更智能、更高效、更专业！** 🦗✨
