#!/bin/bash

# Burp Suite Parameter Security Scanner 构建脚本
# 用于编译和打包插件

set -e

echo "=== Burp Suite Parameter Security Scanner Build Script ==="
echo

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "❌ Java not found. Please install Java 11 or higher."
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 11 ]; then
    echo "❌ Java 11 or higher is required. Current version: $JAVA_VERSION"
    exit 1
fi

echo "✅ Java version: $(java -version 2>&1 | head -n 1)"

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven not found. Please install Apache Maven."
    exit 1
fi

echo "✅ Maven version: $(mvn -version | head -n 1)"
echo

# 清理之前的构建
echo "🧹 Cleaning previous build..."
mvn clean

# 编译项目
echo "🔨 Compiling project..."
mvn compile

# 运行测试
echo "🧪 Running tests..."
mvn test

# 打包项目
echo "📦 Packaging project..."
mvn package

# 检查构建结果
JAR_FILE="target/parameter-security-scanner-1.0.0.jar"
if [ -f "$JAR_FILE" ]; then
    echo
    echo "✅ Build successful!"
    echo "📁 JAR file location: $JAR_FILE"
    echo "📊 JAR file size: $(du -h "$JAR_FILE" | cut -f1)"
    echo
    echo "🚀 Installation Instructions:"
    echo "1. Open Burp Suite"
    echo "2. Go to Extender -> Extensions"
    echo "3. Click 'Add'"
    echo "4. Select 'Java' as extension type"
    echo "5. Choose the JAR file: $JAR_FILE"
    echo "6. Click 'Next' to install"
    echo
    echo "📖 For detailed usage instructions, see README.md"
else
    echo "❌ Build failed! JAR file not found."
    exit 1
fi
