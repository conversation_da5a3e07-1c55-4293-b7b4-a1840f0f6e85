内网IP地址泄露:(?:^|[^0-9])(?:(?:10\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))|(?:172\.(?:1[6-9]|2[0-9]|3[01])\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))|(?:192\.168\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))|(?:127\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))|(?:169\.254\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)))(?:[^0-9]|$)
邮箱地址泄露:[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}
Authorization Header:(?i)authorization:\s*(?:bearer|basic|aws4-hmac-sha256)\s+([A-Za-z0-9/+=_-]{20,})
X-API-Key Header:(?i)x-api-key:\s*([A-Za-z0-9/+=_-]{20,})
Cookie中的Token:(?i)(?:token|auth|session)=([A-Za-z0-9/+=_-]{20,})
URL参数中的密钥:(?i)[?&](access_key|secret_key|api_key|token|key)=([A-Za-z0-9/+=_-]{20,})
.env文件密钥:(?i)^(AWS_ACCESS_KEY_ID|AWS_SECRET_ACCESS_KEY|ALIBABA_CLOUD_ACCESS_KEY_ID|ALIBABA_CLOUD_ACCESS_KEY_SECRET|TENCENTCLOUD_SECRET_ID|TENCENTCLOUD_SECRET_KEY)=(.+)$
YAML配置密钥:(?i)^\s*(access_key|secret_key|api_key|token|password):\s*['"]?([A-Za-z0-9/+=_-]{8,})['"]?$
JSON配置密钥:"(?i)(access_key|secret_key|api_key|token|password)":\s*"([A-Za-z0-9/+=_-]{8,})"
X.509证书:-----BEGIN CERTIFICATE-----[A-Za-z0-9+/\r\n]+-----END CERTIFICATE-----
RSA私钥:-----BEGIN RSA PRIVATE KEY-----[A-Za-z0-9+/\r\n]+-----END RSA PRIVATE KEY-----
OpenSSH私钥:-----BEGIN OPENSSH PRIVATE KEY-----[A-Za-z0-9+/\r\n]+-----END OPENSSH PRIVATE KEY-----
EC私钥:-----BEGIN EC PRIVATE KEY-----[A-Za-z0-9+/\r\n]+-----END EC PRIVATE KEY-----
DSA私钥:-----BEGIN DSA PRIVATE KEY-----[A-Za-z0-9+/\r\n]+-----END DSA PRIVATE KEY-----
GitHub Token:ghp_[A-Za-z0-9]{36}
GitLab Token:glpat-[A-Za-z0-9_-]{20}
Slack Token:xox[baprs]-[A-Za-z0-9-]{10,48}
Discord Token:[MN][A-Za-z\d]{23}\.[\w-]{6}\.[\w-]{27}
Telegram Bot Token:[0-9]{8,10}:[A-Za-z0-9_-]{35}
MongoDB连接字符串:mongodb://[^:]+:([A-Za-z0-9/+=_-]{8,})@[^/]+
MySQL连接字符串:mysql://[^:]+:([A-Za-z0-9/+=_-]{8,})@[^/]+
PostgreSQL连接字符串:postgresql://[^:]+:([A-Za-z0-9/+=_-]{8,})@[^/]+
Redis连接字符串:redis://[^:]*:([A-Za-z0-9/+=_-]{8,})@[^/]+
通用API Key:(?i)(?:api.key|apikey|key|token).{0,20}[:=]\s*['"]?([A-Za-z0-9_-]{20,})['"]?
通用Access Token:(?i)(?:access.token|accesstoken|bearer.token).{0,20}[:=]\s*['"]?([A-Za-z0-9._-]{20,})['"]?
通用Secret Key:(?i)(?:secret.key|secretkey|secret).{0,20}[:=]\s*['"]?([A-Za-z0-9/+=_-]{20,})['"]?
JWT Token:eyJ[A-Za-z0-9_-]*\.eyJ[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*
Bearer Token:(?i)bearer\s+([A-Za-z0-9._-]+)
GCP Service Account Key:"type":\s*"service_account".*?"private_key":\s*"-----BEGIN PRIVATE KEY-----[^"]*-----END PRIVATE KEY-----"
GCP API Key:AIza[0-9A-Za-z_-]{35}
GCP Client ID:[0-9]+-[0-9A-Za-z_-]{32}\.apps\.googleusercontent\.com
Azure Storage Account Key:(?i)(?:azure.{0,20})?(?:account.key|storage.key).{0,20}[:=]\s*['"]?([A-Za-z0-9/+=]{88})['"]?
Azure Client Secret:(?i)(?:azure.{0,20})?(?:client.secret|application.secret).{0,20}[:=]\s*['"]?([A-Za-z0-9~._-]{34,40})['"]?
Azure Subscription ID:(?i)(?:azure.{0,20})?(?:subscription.id|subscription).{0,20}[:=]\s*['"]?([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})['"]?
百度云Access Key:(?i)(?:baidu.{0,20})?(?:access.key|accesskey).{0,20}[:=]\s*['"]?([a-f0-9]{32})['"]?
百度云Secret Key:(?i)(?:baidu.{0,20})?(?:secret.key|secretkey).{0,20}[:=]\s*['"]?([a-f0-9]{32})['"]?
华为云Access Key:(?i)(?:huawei.{0,20})?(?:access.key|accesskey).{0,20}[:=]\s*['"]?([A-Z0-9]{20})['"]?
华为云Secret Key:(?i)(?:huawei.{0,20})?(?:secret.key|secretkey).{0,20}[:=]\s*['"]?([A-Za-z0-9]{40})['"]?
腾讯云SecretId:AKID[a-zA-Z0-9]{32,}
腾讯云SecretKey:(?i)(?:tencent.{0,20})?(?:secret.key|secretkey|app.key).{0,20}[:=]\s*['"]?([a-zA-Z0-9]{32})['"]?
阿里云AccessKey ID:LTAI[a-zA-Z0-9]{12,20}
阿里云AccessKey Secret:(?i)(?:ali.{0,20})?(?:access.secret|accesssecret|secret).{0,20}[:=]\s*['"]?([a-zA-Z0-9]{30})['"]?
AWS Access Key ID:AKIA[0-9A-Z]{16}
AWS Secret Access Key:(?i)(?:aws.{0,20})?(?:secret|secret.key|secret.access.key).{0,20}[:=]\s*['"]?([A-Za-z0-9/+=]{40})['"]?
AWS Session Token:(?i)(?:aws.{0,20})?(?:session|token|session.token).{0,20}[:=]\s*['"]?([A-Za-z0-9/+=]{16,})['"]?
AWS ARN:arn:aws:[a-zA-Z0-9-]+:[a-zA-Z0-9-]*:[0-9]*:[a-zA-Z0-9-_/:.]*