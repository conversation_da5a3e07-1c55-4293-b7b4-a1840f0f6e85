package com.burp.plugin.analyzer;

import burp.*;
import java.util.*;
import java.net.URLDecoder;
import java.io.UnsupportedEncodingException;

/**
 * 参数分析器 - 提取和分析HTTP请求中的参数
 */
public class ParameterAnalyzer {
    
    private final IExtensionHelpers helpers;
    
    public ParameterAnalyzer(IExtensionHelpers helpers) {
        this.helpers = helpers;
    }
    
    /**
     * 从HTTP请求中提取所有参数
     * @param messageInfo HTTP请求响应信息
     * @return 参数名到参数值列表的映射
     */
    public Map<String, List<String>> extractParameters(IHttpRequestResponse messageInfo) {
        Map<String, List<String>> allParameters = new HashMap<>();
        
        IRequestInfo requestInfo = helpers.analyzeRequest(messageInfo);
        
        // 提取URL参数
        Map<String, List<String>> urlParams = extractUrlParameters(requestInfo);
        mergeParameters(allParameters, urlParams);
        
        // 提取POST参数
        if ("POST".equalsIgnoreCase(requestInfo.getMethod()) || 
            "PUT".equalsIgnoreCase(requestInfo.getMethod()) ||
            "PATCH".equalsIgnoreCase(requestInfo.getMethod())) {
            
            Map<String, List<String>> bodyParams = extractBodyParameters(messageInfo);
            mergeParameters(allParameters, bodyParams);
        }
        
        // 提取Cookie参数
        Map<String, List<String>> cookieParams = extractCookieParameters(requestInfo);
        mergeParameters(allParameters, cookieParams);
        
        // 提取Header参数（某些情况下Header也可能包含用户可控数据）
        Map<String, List<String>> headerParams = extractHeaderParameters(requestInfo);
        mergeParameters(allParameters, headerParams);
        
        return allParameters;
    }
    
    /**
     * 提取URL参数
     */
    private Map<String, List<String>> extractUrlParameters(IRequestInfo requestInfo) {
        Map<String, List<String>> parameters = new HashMap<>();
        
        List<IParameter> urlParams = requestInfo.getParameters();
        for (IParameter param : urlParams) {
            if (param.getType() == IParameter.PARAM_URL) {
                String name = param.getName();
                String value = param.getValue();
                
                if (!parameters.containsKey(name)) {
                    parameters.put(name, new ArrayList<String>());
                }
                parameters.get(name).add(value);
            }
        }
        
        return parameters;
    }
    
    /**
     * 提取POST请求体参数
     */
    private Map<String, List<String>> extractBodyParameters(IHttpRequestResponse messageInfo) {
        Map<String, List<String>> parameters = new HashMap<>();
        
        IRequestInfo requestInfo = helpers.analyzeRequest(messageInfo);
        byte[] request = messageInfo.getRequest();
        
        // 获取请求体
        int bodyOffset = requestInfo.getBodyOffset();
        if (bodyOffset >= request.length) {
            return parameters;
        }
        
        String body = helpers.bytesToString(Arrays.copyOfRange(request, bodyOffset, request.length));
        String contentType = getContentType(requestInfo);
        
        if (contentType != null) {
            if (contentType.contains("application/x-www-form-urlencoded")) {
                // 表单数据
                parseFormData(body, parameters);
            } else if (contentType.contains("application/json")) {
                // JSON数据
                parseJsonData(body, parameters);
            } else if (contentType.contains("multipart/form-data")) {
                // 多部分表单数据
                parseMultipartData(body, parameters);
            } else if (contentType.contains("text/xml") || contentType.contains("application/xml")) {
                // XML数据
                parseXmlData(body, parameters);
            }
        }
        
        // 使用Burp的内置参数解析
        List<IParameter> bodyParams = requestInfo.getParameters();
        for (IParameter param : bodyParams) {
            if (param.getType() == IParameter.PARAM_BODY) {
                String name = param.getName();
                String value = param.getValue();
                if (!parameters.containsKey(name)) {
                    parameters.put(name, new ArrayList<String>());
                }
                parameters.get(name).add(value);
            }
        }
        
        return parameters;
    }
    
    /**
     * 提取Cookie参数
     */
    private Map<String, List<String>> extractCookieParameters(IRequestInfo requestInfo) {
        Map<String, List<String>> parameters = new HashMap<>();
        
        List<IParameter> cookieParams = requestInfo.getParameters();
        for (IParameter param : cookieParams) {
            if (param.getType() == IParameter.PARAM_COOKIE) {
                String name = param.getName();
                String value = param.getValue();
                if (!parameters.containsKey(name)) {
                    parameters.put(name, new ArrayList<String>());
                }
                parameters.get(name).add(value);
            }
        }
        
        return parameters;
    }
    
    /**
     * 提取Header参数（某些自定义Header可能包含用户数据）
     */
    private Map<String, List<String>> extractHeaderParameters(IRequestInfo requestInfo) {
        Map<String, List<String>> parameters = new HashMap<>();
        
        List<String> headers = requestInfo.getHeaders();
        for (String header : headers) {
            if (header.contains(":")) {
                String[] parts = header.split(":", 2);
                if (parts.length == 2) {
                    String name = parts[0].trim().toLowerCase();
                    String value = parts[1].trim();
                    
                    // 只提取可能包含用户数据的Header
                    if (isUserControllableHeader(name)) {
                        String headerKey = "header_" + name;
                        if (!parameters.containsKey(headerKey)) {
                            parameters.put(headerKey, new ArrayList<String>());
                        }
                        parameters.get(headerKey).add(value);
                    }
                }
            }
        }
        
        return parameters;
    }
    
    /**
     * 判断Header是否可能包含用户可控数据
     */
    private boolean isUserControllableHeader(String headerName) {
        String[] userHeaders = {
            "x-forwarded-for", "x-real-ip", "x-originating-ip", "x-remote-ip",
            "x-client-ip", "x-forwarded-host", "x-forwarded-proto", "x-requested-with",
            "referer", "user-agent", "authorization", "x-api-key", "x-auth-token"
        };
        
        for (String userHeader : userHeaders) {
            if (headerName.equals(userHeader)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 解析表单数据
     */
    private void parseFormData(String body, Map<String, List<String>> parameters) {
        try {
            String[] pairs = body.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    String key = URLDecoder.decode(keyValue[0], "UTF-8");
                    String value = URLDecoder.decode(keyValue[1], "UTF-8");
                    if (!parameters.containsKey(key)) {
                        parameters.put(key, new ArrayList<String>());
                    }
                    parameters.get(key).add(value);
                }
            }
        } catch (Exception e) {
            // 忽略解析错误
        }
    }
    
    /**
     * 解析JSON数据（简单实现）
     */
    private void parseJsonData(String body, Map<String, List<String>> parameters) {
        // 简单的JSON键值对提取，不使用完整的JSON解析器
        try {
            // 移除花括号和方括号
            String cleaned = body.replaceAll("[{}\\[\\]]", "");
            String[] pairs = cleaned.split(",");
            
            for (String pair : pairs) {
                if (pair.contains(":")) {
                    String[] keyValue = pair.split(":", 2);
                    if (keyValue.length == 2) {
                        String key = keyValue[0].trim().replaceAll("[\"\']", "");
                        String value = keyValue[1].trim().replaceAll("[\"\']", "");
                        String jsonKey = "json_" + key;
                        if (!parameters.containsKey(jsonKey)) {
                            parameters.put(jsonKey, new ArrayList<String>());
                        }
                        parameters.get(jsonKey).add(value);
                    }
                }
            }
        } catch (Exception e) {
            // 忽略解析错误
        }
    }
    
    /**
     * 解析多部分表单数据（简单实现）
     */
    private void parseMultipartData(String body, Map<String, List<String>> parameters) {
        // 简单的multipart解析
        String[] parts = body.split("--");
        for (String part : parts) {
            if (part.contains("name=")) {
                try {
                    String[] lines = part.split("\n");
                    String name = null;
                    String value = null;
                    
                    for (int i = 0; i < lines.length; i++) {
                        String line = lines[i];
                        if (line.contains("name=")) {
                            // 提取name
                            int start = line.indexOf("name=\"") + 6;
                            int end = line.indexOf("\"", start);
                            if (start > 5 && end > start) {
                                name = line.substring(start, end);
                            }
                        } else if (name != null && line.trim().isEmpty() && i + 1 < lines.length) {
                            // 下一行是值
                            value = lines[i + 1].trim();
                            break;
                        }
                    }
                    
                    if (name != null && value != null) {
                        if (!parameters.containsKey(name)) {
                            parameters.put(name, new ArrayList<String>());
                        }
                        parameters.get(name).add(value);
                    }
                } catch (Exception e) {
                    // 忽略解析错误
                }
            }
        }
    }
    
    /**
     * 解析XML数据（简单实现）
     */
    private void parseXmlData(String body, Map<String, List<String>> parameters) {
        // 简单的XML标签值提取
        try {
            String[] lines = body.split("\n");
            for (String line : lines) {
                line = line.trim();
                if (line.startsWith("<") && line.endsWith(">") && line.contains(">") && line.contains("</")) {
                    int start = line.indexOf(">") + 1;
                    int end = line.lastIndexOf("</");
                    if (start < end) {
                        String tagName = line.substring(1, line.indexOf(">"));
                        if (!tagName.contains(" ")) { // 简单标签
                            String value = line.substring(start, end);
                            String xmlKey = "xml_" + tagName;
                            if (!parameters.containsKey(xmlKey)) {
                                parameters.put(xmlKey, new ArrayList<String>());
                            }
                            parameters.get(xmlKey).add(value);
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 忽略解析错误
        }
    }
    
    /**
     * 获取Content-Type
     */
    private String getContentType(IRequestInfo requestInfo) {
        List<String> headers = requestInfo.getHeaders();
        for (String header : headers) {
            if (header.toLowerCase().startsWith("content-type:")) {
                return header.substring(13).trim().toLowerCase();
            }
        }
        return null;
    }
    
    /**
     * 合并参数映射
     */
    private void mergeParameters(Map<String, List<String>> target, Map<String, List<String>> source) {
        for (Map.Entry<String, List<String>> entry : source.entrySet()) {
            String key = entry.getKey();
            if (!target.containsKey(key)) {
                target.put(key, new ArrayList<String>());
            }
            target.get(key).addAll(entry.getValue());
        }
    }
}
