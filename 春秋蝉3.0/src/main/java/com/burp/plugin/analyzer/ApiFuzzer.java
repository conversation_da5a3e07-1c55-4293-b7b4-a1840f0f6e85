package com.burp.plugin.analyzer;

import burp.*;
import com.burp.plugin.core.VulnerabilityManager;
import com.burp.plugin.model.Vulnerability;
import com.burp.plugin.model.VulnerabilityType;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * API模糊测试器 - 检测命令注入、路径遍历等API安全漏洞
 */
public class ApiFuzzer {
    
    private final IBurpExtenderCallbacks callbacks;
    private final IExtensionHelpers helpers;
    private final VulnerabilityManager vulnerabilityManager;
    
    // 命令注入载荷 - 优化为最有效的载荷
    private final String[] commandInjectionPayloads = {
        "; ls", "| ls", "&& ls", "; dir", "| dir", "&& dir",
        "; whoami", "| whoami", "&& whoami",
        "; id", "| id", "`ls`", "$(ls)"
    };
    
    // 路径遍历载荷 - 优化为最有效的载荷
    private final String[] pathTraversalPayloads = {
        "../", "..\\", "....//", "....\\\\",
        "../../../etc/passwd", "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
        "/etc/passwd", "%2e%2e%2f", "..%2f"
    };
    
    // XSS载荷 - 优化为最有效的载荷
    private final String[] xssPayloads = {
        "<script>alert('XSS')</script>",
        "<img src=x onerror=alert('XSS')>",
        "<svg onload=alert('XSS')>",
        "'\"><script>alert('XSS')</script>",
        "\"><img src=x onerror=alert('XSS')>"
    };
    
    // 命令注入错误模式
    private final List<Pattern> commandErrorPatterns;
    
    // 路径遍历成功模式
    private final List<Pattern> pathTraversalPatterns;
    
    // XSS成功模式
    private final List<Pattern> xssPatterns;
    
    public ApiFuzzer(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers, 
                    VulnerabilityManager vulnerabilityManager) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.vulnerabilityManager = vulnerabilityManager;
        
        // 初始化检测模式
        this.commandErrorPatterns = initializeCommandErrorPatterns();
        this.pathTraversalPatterns = initializePathTraversalPatterns();
        this.xssPatterns = initializeXssPatterns();
    }
    
    /**
     * 初始化命令注入错误模式
     */
    private List<Pattern> initializeCommandErrorPatterns() {
        String[] patterns = {
            "sh: .*: command not found",
            "bash: .*: command not found",
            "'.*' is not recognized as an internal or external command",
            "The system cannot find the file specified",
            "No such file or directory",
            "Permission denied",
            "Access is denied",
            "root:.*:0:0:",
            "daemon:.*:1:1:",
            "bin:.*:2:2:",
            "www-data:.*:",
            "nobody:.*:",
            "uid=\\d+\\(.*\\) gid=\\d+\\(.*\\)",
            "Linux.*GNU",
            "Microsoft Windows",
            "PING.*bytes of data",
            "64 bytes from.*time="
        };
        
        List<Pattern> compiledPatterns = new ArrayList<>();
        for (String pattern : patterns) {
            compiledPatterns.add(Pattern.compile(pattern, Pattern.CASE_INSENSITIVE));
        }
        return compiledPatterns;
    }
    
    /**
     * 初始化路径遍历成功模式
     */
    private List<Pattern> initializePathTraversalPatterns() {
        String[] patterns = {
            "root:.*:0:0:",
            "daemon:.*:1:1:",
            "bin:.*:2:2:",
            "\\[boot loader\\]",
            "\\[operating systems\\]",
            "# Copyright.*Microsoft Corp",
            "# This file contains the mappings of IP addresses to host names",
            "127\\.0\\.0\\.1\\s+localhost",
            "::1\\s+localhost"
        };
        
        List<Pattern> compiledPatterns = new ArrayList<>();
        for (String pattern : patterns) {
            compiledPatterns.add(Pattern.compile(pattern, Pattern.CASE_INSENSITIVE));
        }
        return compiledPatterns;
    }
    
    /**
     * 初始化XSS成功模式
     */
    private List<Pattern> initializeXssPatterns() {
        String[] patterns = {
            "<script[^>]*>.*alert\\s*\\(.*\\).*</script>",
            "<img[^>]*onerror\\s*=\\s*['\"]?alert\\s*\\(",
            "<svg[^>]*onload\\s*=\\s*['\"]?alert\\s*\\(",
            "<iframe[^>]*src\\s*=\\s*['\"]?javascript:",
            "<body[^>]*onload\\s*=\\s*['\"]?alert\\s*\\(",
            "<input[^>]*onfocus\\s*=\\s*['\"]?alert\\s*\\(",
            "<select[^>]*onfocus\\s*=\\s*['\"]?alert\\s*\\("
        };
        
        List<Pattern> compiledPatterns = new ArrayList<>();
        for (String pattern : patterns) {
            compiledPatterns.add(Pattern.compile(pattern, Pattern.CASE_INSENSITIVE));
        }
        return compiledPatterns;
    }
    
    /**
     * 测试参数的各种安全漏洞
     */
    public void testParameter(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        // 测试命令注入
        testCommandInjection(originalMessage, paramName, paramValue);
        
        // 测试路径遍历
        testPathTraversal(originalMessage, paramName, paramValue);
        
        // 测试XSS
        testXss(originalMessage, paramName, paramValue);
    }
    
    /**
     * 测试命令注入
     */
    private void testCommandInjection(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        for (String payload : commandInjectionPayloads) {
            try {
                IHttpRequestResponse testMessage = createTestRequest(originalMessage, paramName, paramValue + payload);
                if (testMessage == null) continue;
                
                IHttpRequestResponse response = callbacks.makeHttpRequest(
                    originalMessage.getHttpService(), testMessage.getRequest());
                
                if (response.getResponse() != null) {
                    if (analyzeCommandInjectionResponse(response)) {
                        reportCommandInjection(originalMessage, paramName, payload, response);
                        break;
                    }
                }
                
                Thread.sleep(30);
                
            } catch (Exception e) {
                callbacks.printError("Error testing command injection: " + e.getMessage());
            }
        }
    }
    
    /**
     * 测试路径遍历
     */
    private void testPathTraversal(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        for (String payload : pathTraversalPayloads) {
            try {
                IHttpRequestResponse testMessage = createTestRequest(originalMessage, paramName, payload);
                if (testMessage == null) continue;
                
                IHttpRequestResponse response = callbacks.makeHttpRequest(
                    originalMessage.getHttpService(), testMessage.getRequest());
                
                if (response.getResponse() != null) {
                    if (analyzePathTraversalResponse(response)) {
                        reportPathTraversal(originalMessage, paramName, payload, response);
                        break;
                    }
                }
                
                Thread.sleep(30);
                
            } catch (Exception e) {
                callbacks.printError("Error testing path traversal: " + e.getMessage());
            }
        }
    }
    
    /**
     * 测试XSS
     */
    private void testXss(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        for (String payload : xssPayloads) {
            try {
                IHttpRequestResponse testMessage = createTestRequest(originalMessage, paramName, payload);
                if (testMessage == null) continue;
                
                IHttpRequestResponse response = callbacks.makeHttpRequest(
                    originalMessage.getHttpService(), testMessage.getRequest());
                
                if (response.getResponse() != null) {
                    if (analyzeXssResponse(response, payload)) {
                        reportXss(originalMessage, paramName, payload, response);
                        break;
                    }
                }
                
                Thread.sleep(30);
                
            } catch (Exception e) {
                callbacks.printError("Error testing XSS: " + e.getMessage());
            }
        }
    }
    
    /**
     * 创建测试请求
     */
    private IHttpRequestResponse createTestRequest(IHttpRequestResponse originalMessage, 
                                                  String paramName, String newValue) {
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(originalMessage);
            List<IParameter> parameters = requestInfo.getParameters();
            
            IParameter targetParam = null;
            for (IParameter param : parameters) {
                if (param.getName().equals(paramName)) {
                    targetParam = param;
                    break;
                }
            }
            
            if (targetParam == null) return null;
            
            IParameter newParam = helpers.buildParameter(targetParam.getName(), newValue, targetParam.getType());
            byte[] newRequest = helpers.updateParameter(originalMessage.getRequest(), newParam);
            
            return new IHttpRequestResponse() {
                @Override public byte[] getRequest() { return newRequest; }
                @Override public void setRequest(byte[] message) {}
                @Override public byte[] getResponse() { return null; }
                @Override public void setResponse(byte[] message) {}
                @Override public String getComment() { return null; }
                @Override public void setComment(String comment) {}
                @Override public String getHighlight() { return null; }
                @Override public void setHighlight(String color) {}
                @Override public IHttpService getHttpService() { return originalMessage.getHttpService(); }
                @Override public void setHttpService(IHttpService httpService) {}
            };
            
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 分析命令注入响应
     */
    private boolean analyzeCommandInjectionResponse(IHttpRequestResponse response) {
        String responseString = helpers.bytesToString(response.getResponse());
        
        for (Pattern pattern : commandErrorPatterns) {
            if (pattern.matcher(responseString).find()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 分析路径遍历响应
     */
    private boolean analyzePathTraversalResponse(IHttpRequestResponse response) {
        String responseString = helpers.bytesToString(response.getResponse());
        
        for (Pattern pattern : pathTraversalPatterns) {
            if (pattern.matcher(responseString).find()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 分析XSS响应
     */
    private boolean analyzeXssResponse(IHttpRequestResponse response, String payload) {
        String responseString = helpers.bytesToString(response.getResponse());
        
        // 检查载荷是否被反射到响应中
        if (responseString.contains(payload)) {
            return true;
        }
        
        // 检查XSS模式
        for (Pattern pattern : xssPatterns) {
            if (pattern.matcher(responseString).find()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 报告命令注入漏洞
     */
    private void reportCommandInjection(IHttpRequestResponse originalMessage, String paramName, 
                                       String payload, IHttpRequestResponse testResponse) {
        reportVulnerability(originalMessage, paramName, payload, testResponse, 
                          VulnerabilityType.COMMAND_INJECTION, "Command injection detected");
    }
    
    /**
     * 报告路径遍历漏洞
     */
    private void reportPathTraversal(IHttpRequestResponse originalMessage, String paramName, 
                                   String payload, IHttpRequestResponse testResponse) {
        reportVulnerability(originalMessage, paramName, payload, testResponse, 
                          VulnerabilityType.PATH_TRAVERSAL, "Path traversal detected");
    }
    
    /**
     * 报告XSS漏洞
     */
    private void reportXss(IHttpRequestResponse originalMessage, String paramName, 
                          String payload, IHttpRequestResponse testResponse) {
        reportVulnerability(originalMessage, paramName, payload, testResponse, 
                          VulnerabilityType.XSS, "XSS detected");
    }
    
    /**
     * 通用漏洞报告方法
     */
    private void reportVulnerability(IHttpRequestResponse originalMessage, String paramName, 
                                   String payload, IHttpRequestResponse testResponse, 
                                   VulnerabilityType type, String evidence) {
        IRequestInfo requestInfo = helpers.analyzeRequest(originalMessage);
        String url = requestInfo.getUrl().toString();
        String method = requestInfo.getMethod();
        String responseString = helpers.bytesToString(testResponse.getResponse());
        
        Vulnerability vulnerability = new Vulnerability.Builder()
                .type(type)
                .url(url)
                .parameter(paramName)
                .payload(payload)
                .evidence(evidence)
                .severity(type == VulnerabilityType.COMMAND_INJECTION ? Vulnerability.Severity.CRITICAL : 
                         type == VulnerabilityType.PATH_TRAVERSAL ? Vulnerability.Severity.HIGH : 
                         Vulnerability.Severity.MEDIUM)
                .method(method)
                .originalRequest(helpers.bytesToString(originalMessage.getRequest()))
                .response(responseString.length() > 2000 ? responseString.substring(0, 2000) + "..." : responseString)
                .build();
        
        vulnerabilityManager.addVulnerability(vulnerability);
        
        callbacks.printOutput(type.getDisplayName() + " detected: " + url + " (parameter: " + paramName + ")");
    }
}
