package com.burp.plugin.core;

import com.burp.plugin.model.Vulnerability;
import com.burp.plugin.model.VulnerabilityType;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.net.URL;

/**
 * 漏洞管理器 - 负责存储、管理和分析发现的漏洞
 * 春秋蝉3.0增强功能：支持同域名漏洞合并、搜索、过滤等高级功能
 */
public class VulnerabilityManager {

    private final List<Vulnerability> vulnerabilities;
    private final Map<String, Set<String>> testedUrls; // URL -> 已测试的参数集合
    private final List<VulnerabilityListener> listeners;

    // 🆕 春秋蝉3.0新增功能
    private boolean domainMergeEnabled = true; // 同域名漏洞合并开关
    private final Map<String, List<Vulnerability>> domainGroupedVulns = new ConcurrentHashMap<>();
    private final Set<VulnerabilityChangeListener> changeListeners = ConcurrentHashMap.newKeySet();
    
    public VulnerabilityManager() {
        this.vulnerabilities = new CopyOnWriteArrayList<>();
        this.testedUrls = new ConcurrentHashMap<>();
        this.listeners = new CopyOnWriteArrayList<>();
    }
    
    /**
     * 添加新发现的漏洞
     */
    public void addVulnerability(Vulnerability vulnerability) {
        // 检查是否已存在相同的漏洞
        if (!isDuplicate(vulnerability)) {
            vulnerabilities.add(vulnerability);

            // 🆕 春秋蝉3.0: 更新域名分组
            updateDomainGrouping(vulnerability);

            notifyListeners(vulnerability);

            // 🆕 通知变更监听器
            for (VulnerabilityChangeListener listener : changeListeners) {
                listener.onVulnerabilityAdded(vulnerability);
            }
        }
    }
    
    /**
     * 检查是否为重复漏洞
     */
    private boolean isDuplicate(Vulnerability newVuln) {
        for (Vulnerability existing : vulnerabilities) {
            if (existing.getUrl().equals(newVuln.getUrl()) &&
                existing.getParameter().equals(newVuln.getParameter()) &&
                existing.getType() == newVuln.getType()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 标记URL和参数已被测试
     */
    public void markAsTested(String url, String parameter) {
        // 使用URL的基础部分（去除查询参数）作为key，提高缓存效率
        String baseUrl = getBaseUrl(url);
        if (!testedUrls.containsKey(baseUrl)) {
            testedUrls.put(baseUrl, ConcurrentHashMap.newKeySet());
        }
        testedUrls.get(baseUrl).add(parameter);
    }

    /**
     * 检查URL和参数是否已被测试
     */
    public boolean isAlreadyTested(String url, String parameter) {
        String baseUrl = getBaseUrl(url);
        Set<String> testedParams = testedUrls.get(baseUrl);
        return testedParams != null && testedParams.contains(parameter);
    }

    /**
     * 获取URL的基础部分（去除查询参数和fragment）
     */
    private String getBaseUrl(String url) {
        try {
            int queryIndex = url.indexOf('?');
            int fragmentIndex = url.indexOf('#');

            int endIndex = url.length();
            if (queryIndex != -1) {
                endIndex = Math.min(endIndex, queryIndex);
            }
            if (fragmentIndex != -1) {
                endIndex = Math.min(endIndex, fragmentIndex);
            }

            return url.substring(0, endIndex);
        } catch (Exception e) {
            return url; // 如果解析失败，返回原URL
        }
    }
    
    /**
     * 获取所有漏洞
     */
    public List<Vulnerability> getAllVulnerabilities() {
        return new ArrayList<>(vulnerabilities);
    }
    
    /**
     * 根据类型获取漏洞
     */
    public List<Vulnerability> getVulnerabilitiesByType(VulnerabilityType type) {
        List<Vulnerability> result = new ArrayList<>();
        for (Vulnerability v : vulnerabilities) {
            if (v.getType() == type) {
                result.add(v);
            }
        }
        return result;
    }
    
    /**
     * 获取漏洞统计信息
     */
    public Map<VulnerabilityType, Integer> getVulnerabilityStats() {
        Map<VulnerabilityType, Integer> stats = new HashMap<>();
        for (VulnerabilityType type : VulnerabilityType.values()) {
            stats.put(type, 0);
        }
        
        for (Vulnerability vuln : vulnerabilities) {
            stats.merge(vuln.getType(), 1, Integer::sum);
        }
        
        return stats;
    }
    
    /**
     * 清除所有漏洞记录
     */
    public void clearAll() {
        vulnerabilities.clear();
        testedUrls.clear();
        notifyListenersCleared();
    }
    
    /**
     * 清除特定类型的漏洞
     */
    public void clearByType(VulnerabilityType type) {
        vulnerabilities.removeIf(v -> v.getType() == type);
        notifyListenersCleared();
    }
    
    /**
     * 添加漏洞监听器
     */
    public void addVulnerabilityListener(VulnerabilityListener listener) {
        listeners.add(listener);
    }
    
    /**
     * 移除漏洞监听器
     */
    public void removeVulnerabilityListener(VulnerabilityListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * 通知监听器新漏洞
     */
    private void notifyListeners(Vulnerability vulnerability) {
        for (VulnerabilityListener listener : listeners) {
            try {
                listener.onVulnerabilityFound(vulnerability);
            } catch (Exception e) {
                // 忽略监听器异常，避免影响主流程
            }
        }
    }
    
    /**
     * 通知监听器清除事件
     */
    private void notifyListenersCleared() {
        for (VulnerabilityListener listener : listeners) {
            try {
                listener.onVulnerabilitiesCleared();
            } catch (Exception e) {
                // 忽略监听器异常
            }
        }
    }
    
    /**
     * 漏洞监听器接口
     */
    public interface VulnerabilityListener {
        void onVulnerabilityFound(Vulnerability vulnerability);
        void onVulnerabilitiesCleared();
    }
    
    /**
     * 获取总漏洞数量
     */
    public int getTotalCount() {
        return vulnerabilities.size();
    }
    
    /**
     * 获取高危漏洞数量
     */
    public int getHighRiskCount() {
        int count = 0;
        for (Vulnerability v : vulnerabilities) {
            if (v.getSeverity() == Vulnerability.Severity.HIGH) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * 获取中危漏洞数量
     */
    public int getMediumRiskCount() {
        int count = 0;
        for (Vulnerability v : vulnerabilities) {
            if (v.getSeverity() == Vulnerability.Severity.MEDIUM) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * 获取低危漏洞数量
     */
    public int getLowRiskCount() {
        int count = 0;
        for (Vulnerability v : vulnerabilities) {
            if (v.getSeverity() == Vulnerability.Severity.LOW) {
                count++;
            }
        }
        return count;
    }

    // ==================== 🆕 春秋蝉3.0新增功能 ====================

    /**
     * 更新域名分组
     */
    private void updateDomainGrouping(Vulnerability vulnerability) {
        if (!domainMergeEnabled) {
            return;
        }

        try {
            URL url = new URL(vulnerability.getUrl());
            String domain = url.getHost();

            domainGroupedVulns.computeIfAbsent(domain, k -> new ArrayList<>()).add(vulnerability);
        } catch (Exception e) {
            // 忽略URL解析错误
        }
    }

    /**
     * 获取按域名分组的漏洞
     */
    public Map<String, List<Vulnerability>> getVulnerabilitiesByDomain() {
        // 🔧 修复：总是返回分组数据，不管是否启用域名合并
        // UI层会根据domainMergeEnabled决定如何显示

        // 重新构建分组（确保数据一致性）
        Map<String, List<Vulnerability>> grouped = new HashMap<>();
        for (Vulnerability vuln : vulnerabilities) {
            try {
                URL url = new URL(vuln.getUrl());
                String domain = url.getHost();
                if (domain != null) {
                    grouped.computeIfAbsent(domain, k -> new ArrayList<>()).add(vuln);
                }
            } catch (Exception e) {
                // URL解析失败，使用原始URL作为域名
                String fallbackDomain = vuln.getUrl();
                if (fallbackDomain.length() > 50) {
                    fallbackDomain = fallbackDomain.substring(0, 50) + "...";
                }
                grouped.computeIfAbsent(fallbackDomain, k -> new ArrayList<>()).add(vuln);
            }
        }
        return grouped;
    }

    /**
     * 设置域名合并开关
     */
    public void setDomainMergeEnabled(boolean enabled) {
        boolean oldValue = this.domainMergeEnabled;
        this.domainMergeEnabled = enabled;

        if (enabled && !oldValue) {
            // 重新构建域名分组
            domainGroupedVulns.clear();
            for (Vulnerability vuln : vulnerabilities) {
                updateDomainGrouping(vuln);
            }
        } else if (!enabled && oldValue) {
            // 清空域名分组
            domainGroupedVulns.clear();
        }

        // 通知监听器
        for (VulnerabilityChangeListener listener : changeListeners) {
            listener.onDomainMergeToggled(enabled);
        }
    }

    /**
     * 获取域名合并开关状态
     */
    public boolean isDomainMergeEnabled() {
        return domainMergeEnabled;
    }

    /**
     * 搜索漏洞
     */
    public List<Vulnerability> searchVulnerabilities(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return new ArrayList<>(vulnerabilities);
        }

        String lowerKeyword = keyword.toLowerCase().trim();
        List<Vulnerability> results = new ArrayList<>();

        for (Vulnerability vuln : vulnerabilities) {
            if (matchesKeyword(vuln, lowerKeyword)) {
                results.add(vuln);
            }
        }

        return results;
    }

    /**
     * 检查漏洞是否匹配关键词
     */
    private boolean matchesKeyword(Vulnerability vuln, String keyword) {
        // 检查URL
        if (vuln.getUrl().toLowerCase().contains(keyword)) {
            return true;
        }

        // 检查参数名
        if (vuln.getParameter() != null && vuln.getParameter().toLowerCase().contains(keyword)) {
            return true;
        }

        // 检查载荷
        if (vuln.getPayload() != null && vuln.getPayload().toLowerCase().contains(keyword)) {
            return true;
        }

        // 检查漏洞类型
        if (vuln.getType().getDisplayName().toLowerCase().contains(keyword)) {
            return true;
        }

        // 检查证据
        if (vuln.getEvidence() != null && vuln.getEvidence().toLowerCase().contains(keyword)) {
            return true;
        }

        return false;
    }

    /**
     * 删除指定漏洞
     */
    public boolean removeVulnerability(Vulnerability vulnerability) {
        boolean removed = vulnerabilities.remove(vulnerability);

        if (removed) {
            // 从域名分组中移除
            try {
                URL url = new URL(vulnerability.getUrl());
                String domain = url.getHost();
                List<Vulnerability> domainVulns = domainGroupedVulns.get(domain);
                if (domainVulns != null) {
                    domainVulns.remove(vulnerability);
                    if (domainVulns.isEmpty()) {
                        domainGroupedVulns.remove(domain);
                    }
                }
            } catch (Exception e) {
                // 忽略URL解析错误
            }

            // 通知监听器
            for (VulnerabilityChangeListener listener : changeListeners) {
                listener.onVulnerabilityRemoved(vulnerability);
            }
        }

        return removed;
    }

    /**
     * 清空所有漏洞
     */
    public void clearAllVulnerabilities() {
        vulnerabilities.clear();
        domainGroupedVulns.clear();

        // 通知监听器
        for (VulnerabilityChangeListener listener : changeListeners) {
            listener.onVulnerabilitiesCleared();
        }
    }

    /**
     * 添加变更监听器
     */
    public void addChangeListener(VulnerabilityChangeListener listener) {
        changeListeners.add(listener);
    }

    /**
     * 移除变更监听器
     */
    public void removeChangeListener(VulnerabilityChangeListener listener) {
        changeListeners.remove(listener);
    }

    /**
     * 获取指定域名的漏洞数量
     */
    public int getVulnerabilityCountByDomain(String domain) {
        List<Vulnerability> domainVulns = domainGroupedVulns.get(domain);
        return domainVulns != null ? domainVulns.size() : 0;
    }

    /**
     * 获取所有域名列表
     */
    public Set<String> getAllDomains() {
        Set<String> domains = new HashSet<>();
        for (Vulnerability vuln : vulnerabilities) {
            try {
                URL url = new URL(vuln.getUrl());
                domains.add(url.getHost());
            } catch (Exception e) {
                // 忽略URL解析错误
            }
        }
        return domains;
    }
}
