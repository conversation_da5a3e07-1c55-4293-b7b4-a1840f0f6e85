package com.burp.plugin.core;

import burp.*;
import com.burp.plugin.model.Vulnerability;
import com.burp.plugin.model.VulnerabilityType;
import com.burp.plugin.analyzer.ParameterAnalyzer;
import com.burp.plugin.analyzer.SqlInjectionTester;
import com.burp.plugin.analyzer.ApiFuzzer;
import com.burp.plugin.analyzer.SensitiveInfoDetector;
import com.burp.plugin.analyzer.AdvancedXssTester;
import com.burp.plugin.core.DomainWhitelistManager;

import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * HTTP请求监听器 - 监听Proxy的HTTP History并进行安全测试
 */
public class HttpListener {
    
    private final IBurpExtenderCallbacks callbacks;
    private final IExtensionHelpers helpers;
    private final VulnerabilityManager vulnerabilityManager;
    private final CrawlerManager crawlerManager;
    private final DomainWhitelistManager domainWhitelistManager;
    
    // 分析器组件
    private final ParameterAnalyzer parameterAnalyzer;
    private final SqlInjectionTester sqlInjectionTester;
    private final ApiFuzzer apiFuzzer;
    private final SensitiveInfoDetector sensitiveInfoDetector;
    private final AdvancedXssTester xssTester; // 🆕 春秋蝉3.0: XSS检测器
    
    // 线程池用于异步处理
    private final ExecutorService executorService;
    
    // 功能开关
    private boolean sqlInjectionTestEnabled = true;
    private boolean xssTestEnabled = true; // 🆕 春秋蝉3.0: XSS检测开关
    private boolean apiFuzzingEnabled = true;
    private boolean crawlerEnabled = false;
    private boolean sensitiveInfoDetectionEnabled = true;
    
    public HttpListener(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers,
                       VulnerabilityManager vulnerabilityManager, CrawlerManager crawlerManager,
                       DomainWhitelistManager domainWhitelistManager) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.vulnerabilityManager = vulnerabilityManager;
        this.crawlerManager = crawlerManager;
        this.domainWhitelistManager = domainWhitelistManager;
        
        // 初始化分析器
        this.parameterAnalyzer = new ParameterAnalyzer(helpers);
        this.sqlInjectionTester = new SqlInjectionTester(callbacks, helpers, vulnerabilityManager);
        this.apiFuzzer = new ApiFuzzer(callbacks, helpers, vulnerabilityManager);
        this.sensitiveInfoDetector = new SensitiveInfoDetector(callbacks, helpers, vulnerabilityManager);
        this.xssTester = new AdvancedXssTester(callbacks, helpers, vulnerabilityManager); // 🆕 春秋蝉3.0
        
        // 创建线程池 - 增加并发数提高扫描速度
        this.executorService = Executors.newFixedThreadPool(10);
    }
    
    /**
     * 处理HTTP消息
     */
    public void processHttpMessage(int toolFlag, boolean messageIsRequest, IHttpRequestResponse messageInfo) {
        if (messageIsRequest || messageInfo.getResponse() == null) {
            return;
        }
        
        // 异步处理以避免阻塞Burp
        executorService.submit(() -> {
            try {
                analyzeHttpMessage(messageInfo);
            } catch (Exception e) {
                callbacks.printError("Error analyzing HTTP message: " + e.getMessage());
            }
        });
    }
    
    /**
     * 分析HTTP消息
     */
    private void analyzeHttpMessage(IHttpRequestResponse messageInfo) {
        IRequestInfo requestInfo = helpers.analyzeRequest(messageInfo);

        URL url = requestInfo.getUrl();
        String method = requestInfo.getMethod();
        String host = url.getHost();

        // 检查域名白名单
        if (domainWhitelistManager.isWhitelisted(host)) {
            callbacks.printOutput("跳过白名单域名: " + host);
            return;
        }

        callbacks.printOutput("正在分析请求: " + method + " " + url.toString());

        // 跳过静态资源
        if (isStaticResource(url.getPath())) {
            callbacks.printOutput("跳过静态资源: " + url.getPath());
            return;
        }

        // 提取参数
        Map<String, List<String>> parameters = parameterAnalyzer.extractParameters(messageInfo);

        callbacks.printOutput("发现 " + parameters.size() + " 个参数: " + parameters.keySet());
        
        if (parameters.isEmpty()) {
            return;
        }
        
        // 检查参数污染
        checkParameterPollution(url.toString(), parameters);
        
        // 检查敏感数据暴露
        checkSensitiveDataExposure(messageInfo, url.toString());
        
        // 对每个参数进行安全测试
        for (Map.Entry<String, List<String>> entry : parameters.entrySet()) {
            String paramName = entry.getKey();
            List<String> paramValues = entry.getValue();

            if (paramValues.isEmpty()) {
                continue;
            }

            // 跳过明显不需要测试的参数
            if (shouldSkipParameter(paramName)) {
                continue;
            }

            String paramValue = paramValues.get(0);
            String urlString = url.toString();

            // 检查是否已经测试过
            if (vulnerabilityManager.isAlreadyTested(urlString, paramName)) {
                continue;
            }

            // 标记为已测试
            vulnerabilityManager.markAsTested(urlString, paramName);
            
            // 记录测试前的漏洞数量
            int vulnerabilityCountBefore = vulnerabilityManager.getTotalCount();

            // SQL注入测试
            if (sqlInjectionTestEnabled) {
                sqlInjectionTester.testParameter(messageInfo, paramName, paramValue);
                // 如果发现漏洞，可以选择跳过其他测试以提高速度
                if (vulnerabilityManager.getTotalCount() > vulnerabilityCountBefore) {
                    callbacks.printOutput("在参数 " + paramName + " 中发现漏洞，跳过其他测试以提高速度");
                    continue; // 跳过该参数的其他测试
                }
            }

            // 🆕 春秋蝉3.0: XSS检测
            if (xssTestEnabled) {
                xssTester.testParameter(messageInfo, paramName, paramValue);
                // 如果发现漏洞，可以选择跳过其他测试以提高速度
                if (vulnerabilityManager.getTotalCount() > vulnerabilityCountBefore) {
                    callbacks.printOutput("在参数 " + paramName + " 中发现XSS漏洞，跳过其他测试以提高速度");
                    continue; // 跳过该参数的其他测试
                }
            }

            // API模糊测试
            if (apiFuzzingEnabled) {
                apiFuzzer.testParameter(messageInfo, paramName, paramValue);
            }
        }
        
        // 爬虫功能
        if (crawlerEnabled) {
            crawlerManager.crawlFromResponse(messageInfo);
        }

        // 敏感信息检测（对所有响应进行检测，不限于参数）
        if (sensitiveInfoDetectionEnabled) {
            sensitiveInfoDetector.detectSensitiveInfo(messageInfo);
        }
    }
    
    /**
     * 检查参数污染
     */
    private void checkParameterPollution(String url, Map<String, List<String>> parameters) {
        for (Map.Entry<String, List<String>> entry : parameters.entrySet()) {
            String paramName = entry.getKey();
            List<String> values = entry.getValue();
            
            if (values.size() > 1) {
                // 发现参数污染
                Vulnerability vulnerability = new Vulnerability.Builder()
                        .type(VulnerabilityType.PARAMETER_POLLUTION)
                        .url(url)
                        .parameter(paramName)
                        .evidence("Multiple values found: " + values.toString())
                        .severity(Vulnerability.Severity.MEDIUM)
                        .build();
                
                vulnerabilityManager.addVulnerability(vulnerability);
            }
        }
    }
    
    /**
     * 检查敏感数据暴露
     */
    private void checkSensitiveDataExposure(IHttpRequestResponse messageInfo, String url) {
        String response = helpers.bytesToString(messageInfo.getResponse());
        
        // 检查常见的敏感信息模式
        String[] sensitivePatterns = {
            "password\\s*[=:]\\s*['\"]?\\w+['\"]?",
            "api[_-]?key\\s*[=:]\\s*['\"]?[\\w-]+['\"]?",
            "secret\\s*[=:]\\s*['\"]?\\w+['\"]?",
            "token\\s*[=:]\\s*['\"]?[\\w.-]+['\"]?",
            "\\b\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}\\b", // 信用卡号
            "\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b" // 邮箱
        };
        
        for (String pattern : sensitivePatterns) {
            if (response.matches("(?i).*" + pattern + ".*")) {
                Vulnerability vulnerability = new Vulnerability.Builder()
                        .type(VulnerabilityType.SENSITIVE_DATA_EXPOSURE)
                        .url(url)
                        .parameter("response")
                        .evidence("Sensitive data pattern detected: " + pattern)
                        .severity(Vulnerability.Severity.HIGH)
                        .response(response.length() > 1000 ? response.substring(0, 1000) + "..." : response)
                        .build();
                
                vulnerabilityManager.addVulnerability(vulnerability);
                break; // 只报告第一个匹配的模式
            }
        }
    }
    
    /**
     * 判断是否为静态资源
     */
    private boolean isStaticResource(String path) {
        String[] staticExtensions = {
            // 样式和脚本
            ".css", ".js", ".ts", ".jsx", ".tsx", ".scss", ".sass", ".less",
            // 图片
            ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg", ".bmp", ".webp", ".tiff",
            // 字体
            ".woff", ".woff2", ".ttf", ".eot", ".otf",
            // 文档和压缩包
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
            ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2",
            // 音视频
            ".mp3", ".mp4", ".avi", ".mov", ".wmv", ".flv", ".wav", ".ogg",
            // 其他
            ".xml", ".json", ".txt", ".log", ".map", ".manifest"
        };

        String lowerPath = path.toLowerCase();
        for (String ext : staticExtensions) {
            if (lowerPath.endsWith(ext)) {
                return true;
            }
        }

        // 检查常见静态资源路径
        String[] staticPaths = {
            "/static/", "/assets/", "/public/", "/resources/", "/img/", "/images/",
            "/css/", "/js/", "/fonts/", "/media/", "/uploads/", "/files/"
        };

        for (String staticPath : staticPaths) {
            if (lowerPath.contains(staticPath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断是否应该跳过某个参数的测试
     */
    private boolean shouldSkipParameter(String paramName) {
        if (paramName == null || paramName.isEmpty()) {
            return true;
        }

        String lowerParamName = paramName.toLowerCase();

        // 跳过明显的系统参数和无害参数
        String[] skipParams = {
            // 系统参数
            "timestamp", "time", "_t", "t", "ts", "cachebuster", "cb", "_cb",
            "version", "v", "_v", "build", "debug", "trace",
            // 分页参数
            "page", "pagesize", "limit", "offset", "start", "count", "size",
            // 排序参数
            "sort", "order", "orderby", "sortby", "direction", "dir",
            // 显示参数
            "view", "display", "format", "theme", "skin", "lang", "language", "locale",
            // 跟踪参数
            "utm_source", "utm_medium", "utm_campaign", "utm_content", "utm_term",
            "gclid", "fbclid", "_ga", "_gid", "ref", "referrer",
            // CSRF和安全令牌（通常不需要注入测试）
            "csrf_token", "token", "_token", "authenticity_token", "nonce",
            // 会话相关（通常已经在cookie中）
            "jsessionid", "phpsessid", "asp.net_sessionid"
        };

        for (String skipParam : skipParams) {
            if (lowerParamName.equals(skipParam) || lowerParamName.contains(skipParam)) {
                return true;
            }
        }

        // 跳过明显的数字ID（长度超过10位的纯数字）
        if (paramName.matches("\\d{10,}")) {
            return true;
        }

        return false;
    }

    // Setter方法用于控制功能开关
    public void setSqlInjectionTestEnabled(boolean enabled) {
        this.sqlInjectionTestEnabled = enabled;
    }

    // 🆕 春秋蝉3.0: XSS检测开关
    public void setXssTestEnabled(boolean enabled) {
        this.xssTestEnabled = enabled;
    }

    public void setApiFuzzingEnabled(boolean enabled) {
        this.apiFuzzingEnabled = enabled;
    }
    
    public void setCrawlerEnabled(boolean enabled) {
        this.crawlerEnabled = enabled;
    }

    public void setSensitiveInfoDetectionEnabled(boolean enabled) {
        this.sensitiveInfoDetectionEnabled = enabled;
    }

    public SensitiveInfoDetector getSensitiveInfoDetector() {
        return sensitiveInfoDetector;
    }

    // 🆕 春秋蝉3.0: 获取XSS检测器
    public AdvancedXssTester getXssTester() {
        return xssTester;
    }
    
    /**
     * 关闭资源
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
