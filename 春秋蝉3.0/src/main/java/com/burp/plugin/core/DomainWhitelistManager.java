package com.burp.plugin.core;

import java.util.Set;
import java.util.HashSet;
import java.util.concurrent.ConcurrentHashMap;
import java.io.*;
import java.util.Properties;

/**
 * 域名白名单管理器 - 管理不需要扫描的域名列表，支持持久化存储
 */
public class DomainWhitelistManager {

    private final Set<String> whitelistedDomains;
    private final String configFileName = "domain_whitelist.properties";
    private File configFile;
    
    public DomainWhitelistManager() {
        this.whitelistedDomains = ConcurrentHashMap.newKeySet();
        initializeConfigFile();
        initializeDefaultWhitelist();
        loadCustomWhitelist();
    }
    
    /**
     * 初始化默认白名单
     */
    private void initializeDefaultWhitelist() {
        // 搜索引擎和相关服务
        addDomain("google.com");
        addDomain("google.cn");
        addDomain("googleapis.com");
        addDomain("googleusercontent.com");
        addDomain("gstatic.com");
        addDomain("gvt1.com");
        addDomain("gvt2.com");
        addDomain("gvt3.com");
        addDomain("bing.com");
        addDomain("baidu.com");
        addDomain("yahoo.com");
        addDomain("yandex.com");
        addDomain("duckduckgo.com");
        
        // 代码托管平台
        addDomain("github.com");
        addDomain("gitlab.com");
        addDomain("bitbucket.org");
        addDomain("gitee.com");
        addDomain("coding.net");
        
        // 社交媒体
        addDomain("facebook.com");
        addDomain("twitter.com");
        addDomain("linkedin.com");
        addDomain("instagram.com");
        addDomain("weibo.com");
        addDomain("qq.com");
        addDomain("wechat.com");
        
        // 云服务商
        addDomain("amazonaws.com");
        addDomain("azure.com");
        addDomain("aliyun.com");
        addDomain("qcloud.com");
        addDomain("cloudflare.com");
        
        // 大型网站
        addDomain("youtube.com");
        addDomain("netflix.com");
        addDomain("amazon.com");
        addDomain("taobao.com");
        addDomain("tmall.com");
        addDomain("jd.com");
        addDomain("wikipedia.org");
        
        // CDN和静态资源
        addDomain("jsdelivr.net");
        addDomain("unpkg.com");
        addDomain("cdnjs.cloudflare.com");
        addDomain("ajax.googleapis.com");
        addDomain("fonts.googleapis.com");
        addDomain("fonts.gstatic.com");
        addDomain("bootstrapcdn.com");
        addDomain("jquery.com");
        addDomain("maxcdn.bootstrapcdn.com");
        addDomain("stackpath.bootstrapcdn.com");
        
        // 广告和分析
        addDomain("doubleclick.net");
        addDomain("googleadservices.com");
        addDomain("googlesyndication.com");
        addDomain("google-analytics.com");
        addDomain("googletagmanager.com");
        addDomain("facebook.net");
        
        // 浏览器和系统更新服务
        addDomain("edgedl.me.gvt1.com");
        addDomain("dl.google.com");
        addDomain("update.googleapis.com");
        addDomain("clients2.google.com");
        addDomain("clients4.google.com");
        addDomain("clients6.google.com");
        addDomain("update.microsoft.com");
        addDomain("windowsupdate.microsoft.com");
        addDomain("download.mozilla.org");

        // Google服务的各种子域名模式
        addDomain("gvt1.com");  // 这会匹配 *.gvt1.com 包括 edgedl.me.gvt1.com
        addDomain("gvt2.com");
        addDomain("gvt3.com");
        addDomain("ggpht.com");
        addDomain("googlehosted.com");

        // 其他知名服务
        addDomain("stackoverflow.com");
        addDomain("reddit.com");
        addDomain("medium.com");
        addDomain("zhihu.com");
        addDomain("csdn.net");
        addDomain("jianshu.com");
    }
    
    /**
     * 初始化配置文件路径
     */
    private void initializeConfigFile() {
        try {
            // 获取JAR文件所在目录
            String jarPath = getJarPath();
            if (jarPath != null) {
                File jarDir = new File(jarPath).getParentFile();
                configFile = new File(jarDir, configFileName);
            } else {
                // 如果无法获取JAR路径，使用用户目录
                String userHome = System.getProperty("user.home");
                configFile = new File(userHome, ".burp_domain_whitelist.properties");
            }

            System.out.println("域名白名单配置文件路径: " + configFile.getAbsolutePath());

        } catch (Exception e) {
            System.err.println("初始化域名白名单配置文件失败: " + e.getMessage());
            // 使用临时目录作为备选
            String tempDir = System.getProperty("java.io.tmpdir");
            configFile = new File(tempDir, configFileName);
        }
    }

    /**
     * 获取JAR文件路径
     */
    private String getJarPath() {
        try {
            String className = this.getClass().getName().replace('.', '/') + ".class";
            String classJar = this.getClass().getClassLoader().getResource(className).toString();

            if (classJar.startsWith("jar:")) {
                String jarPath = classJar.substring(4, classJar.indexOf("!/"));
                if (jarPath.startsWith("file:")) {
                    return jarPath.substring(5);
                }
                return jarPath;
            }
        } catch (Exception e) {
            // 忽略错误，返回null
        }
        return null;
    }

    /**
     * 加载自定义白名单
     */
    private void loadCustomWhitelist() {
        if (!configFile.exists()) {
            System.out.println("域名白名单配置文件不存在，将创建新的配置文件");
            return;
        }

        Properties props = new Properties();

        try (FileInputStream fis = new FileInputStream(configFile)) {
            props.load(fis);

            String countStr = props.getProperty("domains.count", "0");
            int count = Integer.parseInt(countStr);

            for (int i = 0; i < count; i++) {
                String domain = props.getProperty("domain." + i);
                if (domain != null && !domain.trim().isEmpty()) {
                    whitelistedDomains.add(domain.toLowerCase().trim());
                }
            }

            System.out.println("已加载 " + count + " 个自定义白名单域名");

        } catch (Exception e) {
            System.err.println("加载自定义域名白名单失败: " + e.getMessage());
        }
    }

    /**
     * 保存自定义白名单
     */
    private void saveCustomWhitelist() {
        try {
            // 获取所有自定义域名（排除内置域名）
            Set<String> builtinDomains = getBuiltinDomains();
            Set<String> customDomains = new HashSet<>();

            for (String domain : whitelistedDomains) {
                if (!builtinDomains.contains(domain)) {
                    customDomains.add(domain);
                }
            }

            Properties props = new Properties();
            props.setProperty("domains.count", String.valueOf(customDomains.size()));

            int index = 0;
            for (String domain : customDomains) {
                props.setProperty("domain." + index, domain);
                index++;
            }

            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(configFile)) {
                props.store(fos, "Burp Suite ChunQiu Chan - Custom Domain Whitelist");
            }

            System.out.println("已保存 " + customDomains.size() + " 个自定义白名单域名到: " + configFile.getAbsolutePath());

        } catch (Exception e) {
            System.err.println("保存自定义域名白名单失败: " + e.getMessage());
        }
    }

    /**
     * 获取内置域名集合
     */
    private Set<String> getBuiltinDomains() {
        Set<String> builtinDomains = new HashSet<>();

        // Google服务域名
        builtinDomains.add("googleapis.com");
        builtinDomains.add("gstatic.com");
        builtinDomains.add("googleusercontent.com");
        builtinDomains.add("google.com");
        builtinDomains.add("googlevideo.com");
        builtinDomains.add("youtube.com");
        builtinDomains.add("ytimg.com");
        builtinDomains.add("gvt1.com");
        builtinDomains.add("gvt2.com");
        builtinDomains.add("gvt3.com");

        // Microsoft服务域名
        builtinDomains.add("microsoft.com");
        builtinDomains.add("microsoftonline.com");
        builtinDomains.add("live.com");
        builtinDomains.add("outlook.com");
        builtinDomains.add("office.com");
        builtinDomains.add("windows.com");

        // 其他常见服务域名
        builtinDomains.add("mozilla.org");
        builtinDomains.add("firefox.com");
        builtinDomains.add("adobe.com");
        builtinDomains.add("apple.com");
        builtinDomains.add("icloud.com");
        builtinDomains.add("cloudflare.com");
        builtinDomains.add("amazonaws.com");
        builtinDomains.add("github.com");
        builtinDomains.add("stackoverflow.com");

        return builtinDomains;
    }

    /**
     * 添加域名到白名单
     */
    public void addDomain(String domain) {
        if (domain != null && !domain.trim().isEmpty()) {
            String normalizedDomain = domain.toLowerCase().trim();
            whitelistedDomains.add(normalizedDomain);

            // 立即保存到文件
            saveCustomWhitelist();

            System.out.println("已添加域名到白名单: " + normalizedDomain);
        }
    }
    
    /**
     * 从白名单移除域名
     */
    public void removeDomain(String domain) {
        if (domain != null) {
            String normalizedDomain = domain.toLowerCase().trim();

            // 检查是否为内置域名
            Set<String> builtinDomains = getBuiltinDomains();
            if (builtinDomains.contains(normalizedDomain)) {
                System.out.println("不能删除内置白名单域名: " + normalizedDomain);
                return;
            }

            boolean removed = whitelistedDomains.remove(normalizedDomain);
            if (removed) {
                // 立即保存到文件
                saveCustomWhitelist();
                System.out.println("已从白名单移除域名: " + normalizedDomain);
            }
        }
    }
    
    /**
     * 检查域名是否在白名单中
     */
    public boolean isWhitelisted(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }

        String lowerDomain = domain.toLowerCase().trim();

        // 直接匹配
        if (whitelistedDomains.contains(lowerDomain)) {
            return true;
        }

        // 检查子域名匹配
        for (String whitelistedDomain : whitelistedDomains) {
            if (lowerDomain.endsWith("." + whitelistedDomain)) {
                return true;
            }
        }

        // 检查复杂域名模式（如 edgedl.me.gvt1.com）
        for (String whitelistedDomain : whitelistedDomains) {
            if (isComplexDomainMatch(lowerDomain, whitelistedDomain)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查复杂域名匹配（处理如 edgedl.me.gvt1.com 这样的域名）
     */
    private boolean isComplexDomainMatch(String domain, String whitelistedDomain) {
        // 特殊处理Google的gvt域名
        if (whitelistedDomain.equals("gvt1.com") || whitelistedDomain.equals("gvt2.com") || whitelistedDomain.equals("gvt3.com")) {
            if (domain.contains("gvt1.com") || domain.contains("gvt2.com") || domain.contains("gvt3.com")) {
                return true;
            }
        }

        // 特殊处理googleapis.com相关域名
        if (whitelistedDomain.equals("googleapis.com")) {
            if (domain.contains("googleapis.com")) {
                return true;
            }
        }

        // 检查是否包含白名单域名的关键部分
        String[] domainParts = domain.split("\\.");
        String[] whitelistParts = whitelistedDomain.split("\\.");

        // 如果域名包含白名单域名的后缀部分
        if (domainParts.length >= whitelistParts.length) {
            boolean match = true;
            for (int i = 0; i < whitelistParts.length; i++) {
                int domainIndex = domainParts.length - whitelistParts.length + i;
                if (!domainParts[domainIndex].equals(whitelistParts[i])) {
                    match = false;
                    break;
                }
            }
            if (match) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * 检查URL是否在白名单中
     */
    public boolean isUrlWhitelisted(String url) {
        try {
            java.net.URL urlObj = new java.net.URL(url);
            return isWhitelisted(urlObj.getHost());
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取所有白名单域名
     */
    public Set<String> getAllWhitelistedDomains() {
        return new java.util.HashSet<>(whitelistedDomains);
    }
    
    /**
     * 获取白名单域名数量
     */
    public int getWhitelistSize() {
        return whitelistedDomains.size();
    }

    /**
     * 获取所有白名单域名
     */
    public Set<String> getAllDomains() {
        return new HashSet<>(whitelistedDomains);
    }

    /**
     * 获取自定义白名单域名数量
     */
    public int getCustomWhitelistSize() {
        Set<String> builtinDomains = getBuiltinDomains();
        int customCount = 0;
        for (String domain : whitelistedDomains) {
            if (!builtinDomains.contains(domain)) {
                customCount++;
            }
        }
        return customCount;
    }

    /**
     * 获取所有自定义域名
     */
    public Set<String> getCustomDomains() {
        Set<String> builtinDomains = getBuiltinDomains();
        Set<String> customDomains = new HashSet<>();

        for (String domain : whitelistedDomains) {
            if (!builtinDomains.contains(domain)) {
                customDomains.add(domain);
            }
        }
        return customDomains;
    }

    /**
     * 清空自定义白名单
     */
    public void clearCustomWhitelist() {
        Set<String> builtinDomains = getBuiltinDomains();
        whitelistedDomains.retainAll(builtinDomains);

        // 保存更改
        saveCustomWhitelist();

        System.out.println("已清空所有自定义白名单域名");
    }

    /**
     * 获取配置文件路径
     */
    public String getConfigFilePath() {
        return configFile.getAbsolutePath();
    }

    /**
     * 检查配置文件是否存在
     */
    public boolean configFileExists() {
        return configFile.exists();
    }
    
    /**
     * 清空白名单
     */
    public void clearWhitelist() {
        whitelistedDomains.clear();
    }
    
    /**
     * 重置为默认白名单
     */
    public void resetToDefault() {
        clearWhitelist();
        initializeDefaultWhitelist();
    }
    
    /**
     * 批量添加域名
     */
    public void addDomains(String[] domains) {
        if (domains != null) {
            for (String domain : domains) {
                addDomain(domain);
            }
        }
    }
    
    /**
     * 从字符串添加域名（逗号分隔）
     */
    public void addDomainsFromString(String domainsString) {
        if (domainsString != null && !domainsString.trim().isEmpty()) {
            String[] domains = domainsString.split("[,;\\s]+");
            addDomains(domains);
        }
    }
    
    /**
     * 导出白名单为字符串
     */
    public String exportWhitelistAsString() {
        return String.join(", ", whitelistedDomains);
    }
}
