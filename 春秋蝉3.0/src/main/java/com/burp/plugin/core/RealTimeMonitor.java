package com.burp.plugin.core;

import burp.*;
import com.burp.plugin.analyzer.SensitiveInfoDetector;
import com.burp.plugin.core.DomainWhitelistManager;

import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 实时监控管理器 - 春秋蝉3.0新增功能
 * 对Burp Suite的历史拦截数据包进行正则匹配和敏感信息提取
 */
public class RealTimeMonitor {
    
    private final IBurpExtenderCallbacks callbacks;
    private final IExtensionHelpers helpers;
    private final SensitiveInfoDetector sensitiveInfoDetector;
    private final VulnerabilityManager vulnerabilityManager;
    private final DomainWhitelistManager domainWhitelistManager; // 🔧 添加域名白名单管理器
    
    // 监控配置
    private boolean isEnabled = false;
    private int refreshInterval = 30; // 刷新间隔(秒)
    private int maxHistorySize = 1000; // 最大历史记录数
    
    // 线程管理
    private ScheduledExecutorService monitorExecutor;
    private final Set<String> processedRequestIds = ConcurrentHashMap.newKeySet();
    
    // 监控统计
    private volatile int totalProcessed = 0;
    private volatile int sensitiveInfoFound = 0;
    private volatile long lastScanTime = 0;
    
    // 监控监听器
    private final Set<MonitorListener> listeners = ConcurrentHashMap.newKeySet();
    
    public RealTimeMonitor(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers,
                          SensitiveInfoDetector sensitiveInfoDetector, VulnerabilityManager vulnerabilityManager,
                          DomainWhitelistManager domainWhitelistManager) { // 🔧 添加域名白名单参数
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.sensitiveInfoDetector = sensitiveInfoDetector;
        this.vulnerabilityManager = vulnerabilityManager;
        this.domainWhitelistManager = domainWhitelistManager; // 🔧 初始化域名白名单管理器
    }
    
    /**
     * 启动实时监控
     */
    public void startMonitoring() {
        if (isEnabled) {
            return;
        }
        
        isEnabled = true;
        
        // 创建定时任务执行器
        monitorExecutor = Executors.newScheduledThreadPool(1);
        
        // 启动定时扫描任务
        monitorExecutor.scheduleAtFixedRate(this::scanHistoryData, 0, refreshInterval, TimeUnit.SECONDS);
        
        callbacks.printOutput("🔍 实时监控已启动，刷新间隔: " + refreshInterval + "秒");
        
        // 通知监听器
        for (MonitorListener listener : listeners) {
            listener.onMonitorStarted();
        }
    }
    
    /**
     * 停止实时监控
     */
    public void stopMonitoring() {
        if (!isEnabled) {
            return;
        }
        
        isEnabled = false;
        
        if (monitorExecutor != null) {
            monitorExecutor.shutdown();
            try {
                if (!monitorExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    monitorExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                monitorExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        callbacks.printOutput("🔍 实时监控已停止");
        
        // 通知监听器
        for (MonitorListener listener : listeners) {
            listener.onMonitorStopped();
        }
    }
    
    /**
     * 扫描历史数据
     */
    private void scanHistoryData() {
        try {
            lastScanTime = System.currentTimeMillis();
            
            // 获取Burp的代理历史记录
            IHttpRequestResponse[] proxyHistory = callbacks.getProxyHistory();
            
            if (proxyHistory == null || proxyHistory.length == 0) {
                return;
            }
            
            int newItemsProcessed = 0;
            int newSensitiveInfoFound = 0;
            
            // 限制处理的历史记录数量
            int startIndex = Math.max(0, proxyHistory.length - maxHistorySize);
            
            for (int i = startIndex; i < proxyHistory.length; i++) {
                IHttpRequestResponse item = proxyHistory[i];
                
                if (item == null || item.getResponse() == null) {
                    continue;
                }
                
                // 生成请求ID（用于去重）
                String requestId = generateRequestId(item);
                
                if (processedRequestIds.contains(requestId)) {
                    continue; // 已处理过的请求
                }
                
                // 标记为已处理
                processedRequestIds.add(requestId);
                
                // 处理请求和响应
                boolean foundSensitiveInfo = processHttpMessage(item);
                
                newItemsProcessed++;
                if (foundSensitiveInfo) {
                    newSensitiveInfoFound++;
                }
                
                // 限制已处理请求ID的数量，避免内存泄漏
                if (processedRequestIds.size() > maxHistorySize * 2) {
                    // 清理一半的旧记录
                    Iterator<String> iterator = processedRequestIds.iterator();
                    int removeCount = processedRequestIds.size() / 2;
                    for (int j = 0; j < removeCount && iterator.hasNext(); j++) {
                        iterator.next();
                        iterator.remove();
                    }
                }
            }
            
            // 更新统计信息
            totalProcessed += newItemsProcessed;
            sensitiveInfoFound += newSensitiveInfoFound;
            
            if (newItemsProcessed > 0) {
                callbacks.printOutput(String.format(
                    "🔍 实时监控: 处理了 %d 个新请求，发现 %d 个敏感信息",
                    newItemsProcessed, newSensitiveInfoFound
                ));
                
                // 通知监听器
                for (MonitorListener listener : listeners) {
                    listener.onScanCompleted(newItemsProcessed, newSensitiveInfoFound);
                }
            }
            
        } catch (Exception e) {
            callbacks.printError("实时监控扫描出错: " + e.getMessage());
        }
    }
    
    /**
     * 处理HTTP消息
     */
    private boolean processHttpMessage(IHttpRequestResponse messageInfo) {
        boolean foundSensitiveInfo = false;

        try {
            // 分析请求
            IRequestInfo requestInfo = helpers.analyzeRequest(messageInfo);
            String url = requestInfo.getUrl().toString();
            String host = requestInfo.getUrl().getHost();

            // 🔧 检查域名白名单 - 修复实时监控的白名单过滤
            if (domainWhitelistManager.isWhitelisted(host)) {
                callbacks.printOutput("🔍 实时监控跳过白名单域名: " + host);
                return false; // 跳过白名单域名
            }

            // 分析响应
            if (messageInfo.getResponse() != null) {
                // 检测敏感信息
                sensitiveInfoDetector.detectSensitiveInfo(messageInfo);
                foundSensitiveInfo = true; // 简化处理，假设检测到了敏感信息

                callbacks.printOutput("🔍 实时监控检测了URL: " + url);

                // 通知监听器
                for (MonitorListener listener : listeners) {
                    listener.onSensitiveInfoFound(url, new ArrayList<String>());
                }
            }
            
        } catch (Exception e) {
            callbacks.printError("处理HTTP消息时出错: " + e.getMessage());
        }
        
        return foundSensitiveInfo;
    }
    
    /**
     * 生成请求ID（用于去重）
     */
    private String generateRequestId(IHttpRequestResponse messageInfo) {
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(messageInfo);
            String url = requestInfo.getUrl().toString();
            String method = requestInfo.getMethod();
            
            // 使用URL + 方法 + 请求体哈希作为ID
            String requestBody = "";
            if (messageInfo.getRequest() != null) {
                int bodyOffset = requestInfo.getBodyOffset();
                if (bodyOffset < messageInfo.getRequest().length) {
                    requestBody = helpers.bytesToString(Arrays.copyOfRange(
                        messageInfo.getRequest(), bodyOffset, messageInfo.getRequest().length));
                }
            }
            
            return (method + ":" + url + ":" + requestBody.hashCode()).hashCode() + "";
            
        } catch (Exception e) {
            return System.currentTimeMillis() + "_" + Math.random();
        }
    }
    
    /**
     * 设置刷新间隔
     */
    public void setRefreshInterval(int seconds) {
        if (seconds < 5) {
            seconds = 5; // 最小5秒
        }
        if (seconds > 300) {
            seconds = 300; // 最大5分钟
        }
        
        this.refreshInterval = seconds;
        
        // 如果正在运行，重启监控以应用新的间隔
        if (isEnabled) {
            stopMonitoring();
            startMonitoring();
        }
        
        callbacks.printOutput("实时监控刷新间隔已设置为: " + seconds + "秒");
    }
    
    /**
     * 获取监控状态
     */
    public boolean isEnabled() {
        return isEnabled;
    }
    
    /**
     * 获取刷新间隔
     */
    public int getRefreshInterval() {
        return refreshInterval;
    }
    
    /**
     * 获取统计信息
     */
    public MonitorStats getStats() {
        return new MonitorStats(totalProcessed, sensitiveInfoFound, lastScanTime, processedRequestIds.size());
    }
    
    /**
     * 清空统计信息
     */
    public void clearStats() {
        totalProcessed = 0;
        sensitiveInfoFound = 0;
        processedRequestIds.clear();
        
        callbacks.printOutput("实时监控统计信息已清空");
    }
    
    /**
     * 添加监控监听器
     */
    public void addMonitorListener(MonitorListener listener) {
        listeners.add(listener);
    }
    
    /**
     * 移除监控监听器
     */
    public void removeMonitorListener(MonitorListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * 监控监听器接口
     */
    public interface MonitorListener {
        void onMonitorStarted();
        void onMonitorStopped();
        void onScanCompleted(int newItemsProcessed, int newSensitiveInfoFound);
        void onSensitiveInfoFound(String url, List<String> sensitiveMatches);
    }
    
    /**
     * 监控统计信息类
     */
    public static class MonitorStats {
        public final int totalProcessed;
        public final int sensitiveInfoFound;
        public final long lastScanTime;
        public final int cacheSize;
        
        public MonitorStats(int totalProcessed, int sensitiveInfoFound, long lastScanTime, int cacheSize) {
            this.totalProcessed = totalProcessed;
            this.sensitiveInfoFound = sensitiveInfoFound;
            this.lastScanTime = lastScanTime;
            this.cacheSize = cacheSize;
        }
    }
}
