package com.burp.plugin.core;

import com.burp.plugin.model.Vulnerability;

/**
 * 漏洞变更监听器 - 春秋蝉3.0新增
 * 用于监听漏洞的添加、删除、更新等操作
 */
public interface VulnerabilityChangeListener {
    
    /**
     * 漏洞添加时触发
     */
    void onVulnerabilityAdded(Vulnerability vulnerability);
    
    /**
     * 漏洞删除时触发
     */
    void onVulnerabilityRemoved(Vulnerability vulnerability);
    
    /**
     * 漏洞更新时触发
     */
    void onVulnerabilityUpdated(Vulnerability oldVuln, Vulnerability newVuln);
    
    /**
     * 漏洞列表清空时触发
     */
    void onVulnerabilitiesCleared();
    
    /**
     * 域名合并状态变更时触发
     */
    void onDomainMergeToggled(boolean enabled);
}
