package com.burp.plugin.core;

import burp.*;
import com.burp.plugin.analyzer.AdvancedSqlInjectionTester;
import com.burp.plugin.analyzer.AdvancedXssTester;
import com.burp.plugin.analyzer.AdvancedApiFuzzer;
import com.burp.plugin.analyzer.ParameterAnalyzer;

import java.net.URL;
import java.util.*;
import java.util.concurrent.*;

/**
 * 主动扫描管理器 - 将爬虫发现的URL自动加入安全测试队列
 * 实现真正的主动扫描能力，发现隐藏的0day漏洞
 */
public class ActiveScanManager {
    
    private final IBurpExtenderCallbacks callbacks;
    private final IExtensionHelpers helpers;
    private final VulnerabilityManager vulnerabilityManager;
    private final DomainWhitelistManager domainWhitelistManager;
    
    // 扫描器组件
    private final AdvancedSqlInjectionTester sqlInjectionTester;
    private final AdvancedXssTester xssTester;
    private final AdvancedApiFuzzer apiFuzzer;
    private final ParameterAnalyzer parameterAnalyzer;
    
    // 扫描任务队列
    private final BlockingQueue<ScanTask> scanQueue = new LinkedBlockingQueue<>();
    private final Set<String> scannedUrls = ConcurrentHashMap.newKeySet();
    private final Set<String> pendingUrls = ConcurrentHashMap.newKeySet();
    
    // 线程池
    private final ExecutorService scanExecutor = Executors.newFixedThreadPool(3); // 3个并发扫描线程
    private final ScheduledExecutorService scheduledExecutor = Executors.newScheduledThreadPool(1);
    
    // 扫描配置
    private boolean isEnabled = true;
    private int maxConcurrentScans = 3;
    private int scanDelay = 1000; // 扫描间隔(毫秒)
    private int maxUrlsPerDomain = 50; // 每个域名最大扫描URL数
    
    // 统计信息
    private volatile int totalScanned = 0;
    private volatile int vulnerabilitiesFound = 0;
    private volatile long scanStartTime = 0;
    
    public ActiveScanManager(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers,
                            VulnerabilityManager vulnerabilityManager, 
                            DomainWhitelistManager domainWhitelistManager) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.vulnerabilityManager = vulnerabilityManager;
        this.domainWhitelistManager = domainWhitelistManager;
        
        // 初始化扫描器
        this.sqlInjectionTester = new AdvancedSqlInjectionTester(callbacks, helpers, vulnerabilityManager);
        this.xssTester = new AdvancedXssTester(callbacks, helpers, vulnerabilityManager);
        this.apiFuzzer = new AdvancedApiFuzzer(callbacks, helpers, vulnerabilityManager);
        this.parameterAnalyzer = new ParameterAnalyzer(helpers);
        
        // 启动扫描工作线程
        startScanWorkers();
        
        // 启动统计报告线程
        startStatsReporter();
        
        callbacks.printOutput("主动扫描管理器已启动 - 爬虫发现的URL将自动进行安全测试");
    }
    
    /**
     * 启动扫描工作线程
     */
    private void startScanWorkers() {
        for (int i = 0; i < maxConcurrentScans; i++) {
            final int workerId = i + 1;
            scanExecutor.submit(() -> {
                callbacks.printOutput("扫描工作线程 " + workerId + " 已启动");
                
                while (!Thread.currentThread().isInterrupted()) {
                    try {
                        ScanTask task = scanQueue.take(); // 阻塞等待任务
                        
                        if (isEnabled) {
                            executeScanTask(task, workerId);
                        }
                        
                        Thread.sleep(scanDelay); // 扫描间隔
                        
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    } catch (Exception e) {
                        callbacks.printError("扫描工作线程 " + workerId + " 错误: " + e.getMessage());
                    }
                }
                
                callbacks.printOutput("扫描工作线程 " + workerId + " 已停止");
            });
        }
    }
    
    /**
     * 启动统计报告线程
     */
    private void startStatsReporter() {
        scheduledExecutor.scheduleAtFixedRate(() -> {
            if (totalScanned > 0) {
                long elapsedTime = System.currentTimeMillis() - scanStartTime;
                double scansPerMinute = (double) totalScanned / (elapsedTime / 60000.0);
                
                callbacks.printOutput(String.format(
                    "📊 主动扫描统计: 已扫描 %d 个URL, 发现 %d 个漏洞, 扫描速度 %.1f URL/分钟, 队列中 %d 个任务",
                    totalScanned, vulnerabilitiesFound, scansPerMinute, scanQueue.size()
                ));
            }
        }, 60, 60, TimeUnit.SECONDS); // 每分钟报告一次
    }
    
    /**
     * 爬虫发现新URL时调用此方法
     */
    public void onUrlDiscovered(String url) {
        try {
            if (!isEnabled) {
                return;
            }
            
            // 检查URL是否已经扫描过
            if (scannedUrls.contains(url) || pendingUrls.contains(url)) {
                return;
            }
            
            // 检查域名白名单
            URL urlObj = new URL(url);
            if (domainWhitelistManager.isWhitelisted(urlObj.getHost())) {
                callbacks.printOutput("跳过白名单域名的主动扫描: " + urlObj.getHost());
                return;
            }
            
            // 检查每个域名的URL数量限制
            String domain = urlObj.getHost();
            long domainUrlCount = scannedUrls.stream()
                .filter(scannedUrl -> {
                    try {
                        return new URL(scannedUrl).getHost().equals(domain);
                    } catch (Exception e) {
                        return false;
                    }
                })
                .count();
            
            if (domainUrlCount >= maxUrlsPerDomain) {
                callbacks.printOutput("域名 " + domain + " 已达到最大扫描URL数限制: " + maxUrlsPerDomain);
                return;
            }
            
            // 添加到待扫描队列
            pendingUrls.add(url);
            ScanTask task = new ScanTask(url, ScanType.FULL_SCAN);
            scanQueue.offer(task);
            
            if (scanStartTime == 0) {
                scanStartTime = System.currentTimeMillis();
            }
            
            callbacks.printOutput("🎯 新URL已加入主动扫描队列: " + url + " (队列长度: " + scanQueue.size() + ")");
            
        } catch (Exception e) {
            callbacks.printError("处理新发现URL时出错: " + e.getMessage());
        }
    }
    
    /**
     * 执行扫描任务
     */
    private void executeScanTask(ScanTask task, int workerId) {
        try {
            callbacks.printOutput("🔍 工作线程 " + workerId + " 开始扫描: " + task.url);
            
            // 标记为已扫描
            scannedUrls.add(task.url);
            pendingUrls.remove(task.url);
            
            // 记录扫描前的漏洞数量
            int vulnerabilitiesBefore = vulnerabilityManager.getTotalCount();
            
            // 发送HTTP请求获取页面内容
            IHttpRequestResponse response = sendHttpRequest(task.url);
            if (response == null) {
                callbacks.printOutput("无法访问URL: " + task.url);
                return;
            }
            
            // 分析参数
            Map<String, List<String>> parameters = parameterAnalyzer.extractParameters(response);
            
            if (parameters.isEmpty()) {
                callbacks.printOutput("URL无参数，跳过安全测试: " + task.url);
                return;
            }
            
            callbacks.printOutput("发现 " + parameters.size() + " 个参数，开始安全测试: " + task.url);
            
            // 对每个参数进行全面安全测试
            for (Map.Entry<String, List<String>> entry : parameters.entrySet()) {
                String paramName = entry.getKey();
                List<String> paramValues = entry.getValue();
                
                if (paramValues.isEmpty()) {
                    continue;
                }
                
                String paramValue = paramValues.get(0);
                
                try {
                    // 1. SQL注入检测
                    sqlInjectionTester.testParameter(response, paramName, paramValue);
                    
                    // 2. XSS检测
                    xssTester.testParameter(response, paramName, paramValue);
                    
                    // 3. API模糊测试 (寻找0day)
                    apiFuzzer.testParameter(response, paramName, paramValue);
                    
                    Thread.sleep(200); // 参数间隔
                    
                } catch (Exception e) {
                    callbacks.printError("测试参数 " + paramName + " 时出错: " + e.getMessage());
                }
            }
            
            // 统计新发现的漏洞
            int vulnerabilitiesAfter = vulnerabilityManager.getTotalCount();
            int newVulnerabilities = vulnerabilitiesAfter - vulnerabilitiesBefore;
            
            if (newVulnerabilities > 0) {
                vulnerabilitiesFound += newVulnerabilities;
                callbacks.printOutput("🎉 在 " + task.url + " 发现 " + newVulnerabilities + " 个新漏洞!");
            }
            
            totalScanned++;
            callbacks.printOutput("✅ 工作线程 " + workerId + " 完成扫描: " + task.url + 
                                 " (参数: " + parameters.size() + ", 新漏洞: " + newVulnerabilities + ")");
            
        } catch (Exception e) {
            callbacks.printError("执行扫描任务时出错: " + e.getMessage());
        }
    }
    
    /**
     * 发送HTTP请求
     */
    private IHttpRequestResponse sendHttpRequest(String url) {
        try {
            URL urlObj = new URL(url);
            
            // 构建HTTP请求
            String request = "GET " + urlObj.getPath() + 
                           (urlObj.getQuery() != null ? "?" + urlObj.getQuery() : "") + 
                           " HTTP/1.1\r\n" +
                           "Host: " + urlObj.getHost() + "\r\n" +
                           "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\r\n" +
                           "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\r\n" +
                           "Accept-Language: en-US,en;q=0.5\r\n" +
                           "Accept-Encoding: gzip, deflate\r\n" +
                           "Connection: close\r\n\r\n";
            
            // 创建HTTP服务
            IHttpService httpService = helpers.buildHttpService(
                urlObj.getHost(), 
                urlObj.getPort() == -1 ? (urlObj.getProtocol().equals("https") ? 443 : 80) : urlObj.getPort(),
                urlObj.getProtocol().equals("https")
            );
            
            // 发送请求
            return callbacks.makeHttpRequest(httpService, request.getBytes());
            
        } catch (Exception e) {
            callbacks.printError("发送HTTP请求失败: " + url + " - " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取扫描统计信息
     */
    public ScanStats getStats() {
        return new ScanStats(totalScanned, vulnerabilitiesFound, scanQueue.size(), scannedUrls.size());
    }
    
    /**
     * 启用/禁用主动扫描
     */
    public void setEnabled(boolean enabled) {
        this.isEnabled = enabled;
        callbacks.printOutput("主动扫描已" + (enabled ? "启用" : "禁用"));
    }
    
    /**
     * 清空扫描队列
     */
    public void clearQueue() {
        scanQueue.clear();
        pendingUrls.clear();
        callbacks.printOutput("扫描队列已清空");
    }
    
    /**
     * 关闭扫描管理器
     */
    public void shutdown() {
        isEnabled = false;
        scanExecutor.shutdown();
        scheduledExecutor.shutdown();
        callbacks.printOutput("主动扫描管理器已关闭");
    }
    
    /**
     * 扫描任务类
     */
    private static class ScanTask {
        public final String url;
        public final ScanType type;
        public final long createTime;
        
        public ScanTask(String url, ScanType type) {
            this.url = url;
            this.type = type;
            this.createTime = System.currentTimeMillis();
        }
    }
    
    /**
     * 扫描类型枚举
     */
    private enum ScanType {
        FULL_SCAN,      // 全面扫描
        QUICK_SCAN,     // 快速扫描
        PARAMETER_ONLY  // 仅参数扫描
    }
    
    /**
     * 扫描统计信息类
     */
    public static class ScanStats {
        public final int totalScanned;
        public final int vulnerabilitiesFound;
        public final int queueSize;
        public final int scannedUrlsCount;
        
        public ScanStats(int totalScanned, int vulnerabilitiesFound, int queueSize, int scannedUrlsCount) {
            this.totalScanned = totalScanned;
            this.vulnerabilitiesFound = vulnerabilitiesFound;
            this.queueSize = queueSize;
            this.scannedUrlsCount = scannedUrlsCount;
        }
    }
}
