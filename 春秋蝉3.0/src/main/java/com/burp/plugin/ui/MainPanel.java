package com.burp.plugin.ui;

import com.burp.plugin.<PERSON><PERSON><PERSON><PERSON><PERSON>;
import com.burp.plugin.core.VulnerabilityManager;
import com.burp.plugin.model.Vulnerability;
import com.burp.plugin.model.VulnerabilityType;
import com.burp.plugin.analyzer.SensitiveInfoDetector;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableRowSorter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.Map;
import java.awt.event.*;
import java.awt.datatransfer.StringSelection;
import java.awt.datatransfer.Clipboard;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import com.burp.plugin.core.VulnerabilityChangeListener;
import com.burp.plugin.core.RealTimeMonitor;
import com.burp.plugin.ui.VulnerabilityDetailPanel;
import javax.swing.RowFilter;
import javax.swing.BoxLayout;
import javax.swing.Box;
import javax.swing.RowSorter;
import javax.swing.SortOrder;
import java.util.HashSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.regex.Pattern;

/**
 * 插件主界面 - 春秋蝉3.0增强版
 * 新增功能：搜索、XSS开关、右键菜单、域名合并、实时刷新等
 */
public class MainPanel extends JPanel implements VulnerabilityManager.VulnerabilityListener, VulnerabilityChangeListener {

    private final ChunQiuChan plugin;
    private final VulnerabilityManager vulnerabilityManager;

    // UI组件
    private JTable vulnerabilityTable;
    private DefaultTableModel tableModel;
    private TableRowSorter<DefaultTableModel> tableSorter;
    private JLabel statsLabel;
    private JTextArea detailsArea;
    private JCheckBox enabledCheckBox;
    private JCheckBox sqlInjectionCheckBox;
    private JCheckBox apiFuzzingCheckBox;

    // 🆕 春秋蝉3.0新增UI组件
    private JTextField searchField;
    private JCheckBox domainMergeCheckBox;
    private JCheckBox xssTestCheckBox;
    private JButton refreshButton;
    private JPopupMenu rightClickMenu;
    private JLabel searchResultLabel;
    private VulnerabilityDetailPanel detailPanel;
    private RealTimeMonitor realTimeMonitor;
    private JCheckBox realTimeMonitorCheckBox;
    private JSpinner refreshIntervalSpinner;
    private JCheckBox crawlerCheckBox;
    private JCheckBox sensitiveInfoCheckBox;
    
    // 统计标签
    private JLabel totalLabel;
    private JLabel highLabel;
    private JLabel mediumLabel;
    private JLabel lowLabel;
    
    public MainPanel(ChunQiuChan plugin, VulnerabilityManager vulnerabilityManager) {
        this.plugin = plugin;
        this.vulnerabilityManager = vulnerabilityManager;

        // 注册为漏洞监听器
        vulnerabilityManager.addVulnerabilityListener(this);

        // 🆕 春秋蝉3.0: 注册为变更监听器
        vulnerabilityManager.addChangeListener(this);

        // 🆕 春秋蝉3.0: 初始化实时监控
        initializeRealTimeMonitor();

        initializeUI();
        updateStats();
    }
    
    /**
     * 初始化UI界面
     */
    private void initializeUI() {
        setLayout(new BorderLayout());
        
        // 创建顶部控制面板
        JPanel controlPanel = createControlPanel();
        add(controlPanel, BorderLayout.NORTH);
        
        // 创建中间的分割面板
        JSplitPane splitPane = createSplitPane();
        add(splitPane, BorderLayout.CENTER);
        
        // 创建底部状态面板
        JPanel statusPanel = createStatusPanel();
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建控制面板 - 🆕 春秋蝉3.0重新设计多行布局
     */
    private JPanel createControlPanel() {
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBorder(BorderFactory.createTitledBorder("春秋蝉 3.0 控制面板"));

        // 创建多行布局容器
        JPanel contentPanel = new JPanel();
        contentPanel.setLayout(new BoxLayout(contentPanel, BoxLayout.Y_AXIS));

        // 第一行：基础控制
        JPanel row1 = createControlRow1();
        contentPanel.add(row1);

        // 第二行：功能开关
        JPanel row2 = createControlRow2();
        contentPanel.add(row2);

        // 第三行：高级功能
        JPanel row3 = createControlRow3();
        contentPanel.add(row3);

        // 第四行：操作按钮
        JPanel row4 = createControlRow4();
        contentPanel.add(row4);

        mainPanel.add(contentPanel, BorderLayout.CENTER);
        return mainPanel;
    }

    /**
     * 创建第一行：基础控制
     */
    private JPanel createControlRow1() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT, 5, 2));
        panel.setBorder(BorderFactory.createTitledBorder("基础控制"));

        // 插件总开关
        enabledCheckBox = new JCheckBox("🔌 启用插件", plugin.isEnabled());
        enabledCheckBox.addActionListener(e -> plugin.setEnabled(enabledCheckBox.isSelected()));
        enabledCheckBox.setFont(enabledCheckBox.getFont().deriveFont(Font.BOLD));
        panel.add(enabledCheckBox);

        return panel;
    }

    /**
     * 创建第二行：功能开关
     */
    private JPanel createControlRow2() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT, 5, 2));
        panel.setBorder(BorderFactory.createTitledBorder("检测功能"));

        // SQL注入检测
        sqlInjectionCheckBox = new JCheckBox("🛡️ SQL注入检测", plugin.isSqlInjectionTestEnabled());
        sqlInjectionCheckBox.addActionListener(e -> plugin.setSqlInjectionTestEnabled(sqlInjectionCheckBox.isSelected()));
        panel.add(sqlInjectionCheckBox);

        // 🆕 春秋蝉3.0: XSS检测开关
        xssTestCheckBox = new JCheckBox("⚡ XSS检测", plugin.isXssTestEnabled());
        xssTestCheckBox.addActionListener(e -> plugin.setXssTestEnabled(xssTestCheckBox.isSelected()));
        panel.add(xssTestCheckBox);

        // API模糊测试
        apiFuzzingCheckBox = new JCheckBox("🔍 API模糊测试", plugin.isApiFuzzingEnabled());
        apiFuzzingCheckBox.addActionListener(e -> plugin.setApiFuzzingEnabled(apiFuzzingCheckBox.isSelected()));
        panel.add(apiFuzzingCheckBox);

        // 同域爬虫
        crawlerCheckBox = new JCheckBox("🕷️ 同域爬虫", plugin.isCrawlerEnabled());
        crawlerCheckBox.addActionListener(e -> plugin.setCrawlerEnabled(crawlerCheckBox.isSelected()));
        panel.add(crawlerCheckBox);

        // 敏感信息检测
        sensitiveInfoCheckBox = new JCheckBox("🔐 敏感信息检测", plugin.isSensitiveInfoDetectionEnabled());
        sensitiveInfoCheckBox.addActionListener(e -> plugin.setSensitiveInfoDetectionEnabled(sensitiveInfoCheckBox.isSelected()));
        panel.add(sensitiveInfoCheckBox);

        return panel;
    }

    /**
     * 创建第三行：高级功能
     */
    private JPanel createControlRow3() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT, 5, 2));
        panel.setBorder(BorderFactory.createTitledBorder("高级功能"));

        // 🆕 春秋蝉3.0: 域名合并开关
        domainMergeCheckBox = new JCheckBox("📁 同域名合并", vulnerabilityManager.isDomainMergeEnabled());
        domainMergeCheckBox.addActionListener(e -> {
            vulnerabilityManager.setDomainMergeEnabled(domainMergeCheckBox.isSelected());
            refreshVulnerabilityTable();
        });
        domainMergeCheckBox.setToolTipText("将相同域名的漏洞进行分组显示");
        panel.add(domainMergeCheckBox);

        // 🆕 春秋蝉3.0: 实时监控功能
        realTimeMonitorCheckBox = new JCheckBox("⏱️ 实时监控", false);
        realTimeMonitorCheckBox.addActionListener(e -> toggleRealTimeMonitor());
        realTimeMonitorCheckBox.setToolTipText("对Burp历史数据包进行实时敏感信息检测");
        panel.add(realTimeMonitorCheckBox);

        // 刷新间隔设置
        panel.add(new JLabel("间隔:"));
        refreshIntervalSpinner = new JSpinner(new SpinnerNumberModel(30, 5, 300, 5));
        refreshIntervalSpinner.setToolTipText("实时监控刷新间隔(秒)");
        refreshIntervalSpinner.addChangeListener(e -> updateRefreshInterval());
        refreshIntervalSpinner.setPreferredSize(new Dimension(60, 25));
        panel.add(refreshIntervalSpinner);
        panel.add(new JLabel("秒"));

        // 🆕 春秋蝉3.0: 搜索功能
        panel.add(Box.createHorizontalStrut(10)); // 添加间距
        panel.add(new JLabel("🔍 搜索:"));
        searchField = new JTextField(12);
        searchField.setToolTipText("搜索URL、参数、载荷、漏洞类型等");
        searchField.getDocument().addDocumentListener(new DocumentListener() {
            @Override
            public void insertUpdate(DocumentEvent e) { performSearch(); }
            @Override
            public void removeUpdate(DocumentEvent e) { performSearch(); }
            @Override
            public void changedUpdate(DocumentEvent e) { performSearch(); }
        });
        panel.add(searchField);

        searchResultLabel = new JLabel("");
        searchResultLabel.setForeground(Color.BLUE);
        panel.add(searchResultLabel);

        return panel;
    }

    /**
     * 创建第四行：操作按钮
     */
    private JPanel createControlRow4() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT, 5, 2));
        panel.setBorder(BorderFactory.createTitledBorder("操作按钮"));

        // 🆕 春秋蝉3.0: 刷新按钮
        refreshButton = new JButton("🔄 刷新");
        refreshButton.addActionListener(e -> refreshVulnerabilityTable());
        refreshButton.setToolTipText("刷新漏洞列表");
        panel.add(refreshButton);

        // 清空记录按钮
        JButton clearButton = new JButton("🗑️ 清空记录");
        clearButton.addActionListener(e -> {
            int result = JOptionPane.showConfirmDialog(this,
                "确定要清空所有漏洞记录吗？",
                "确认清空", JOptionPane.YES_NO_OPTION);
            if (result == JOptionPane.YES_OPTION) {
                vulnerabilityManager.clearAllVulnerabilities();
                refreshVulnerabilityTable();
            }
        });
        clearButton.setBackground(new Color(255, 200, 200));
        panel.add(clearButton);

        // 导出报告按钮
        JButton exportButton = new JButton("📊 导出报告");
        exportButton.addActionListener(e -> exportReport());
        exportButton.setBackground(new Color(200, 255, 200));
        panel.add(exportButton);

        // 添加分隔符
        panel.add(Box.createHorizontalStrut(10));

        // 域名白名单按钮
        JButton whitelistButton = new JButton("🌐 域名白名单");
        whitelistButton.addActionListener(e -> showDomainWhitelistDialog());
        whitelistButton.setBackground(new Color(200, 220, 255));
        panel.add(whitelistButton);

        // 敏感信息规则按钮
        JButton sensitiveRulesButton = new JButton("🔐 敏感信息规则");
        sensitiveRulesButton.addActionListener(e -> showSensitiveRulesDialog());
        sensitiveRulesButton.setBackground(new Color(255, 220, 200));
        panel.add(sensitiveRulesButton);

        return panel;
    }
    
    /**
     * 创建分割面板
     */
    private JSplitPane createSplitPane() {
        // 创建漏洞表格
        JPanel tablePanel = createTablePanel();
        
        // 创建详情面板
        JPanel detailsPanel = createDetailsPanel();
        
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT, tablePanel, detailsPanel);
        splitPane.setDividerLocation(300);
        splitPane.setResizeWeight(0.7);
        
        return splitPane;
    }
    
    /**
     * 创建表格面板 - 🆕 春秋蝉3.0增强版
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // 创建标题面板
        JPanel titlePanel = new JPanel(new BorderLayout());
        titlePanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createTitledBorder("🔍 发现的安全漏洞"),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));

        // 添加表格工具栏
        JPanel toolbarPanel = createTableToolbar();
        titlePanel.add(toolbarPanel, BorderLayout.EAST);

        panel.add(titlePanel, BorderLayout.NORTH);

        // 创建表格模型
        String[] columnNames = {"漏洞类型", "严重程度", "URL地址", "参数名称", "发现时间"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        vulnerabilityTable = new JTable(tableModel);
        vulnerabilityTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        vulnerabilityTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                showVulnerabilityDetails();
            }
        });

        // 🆕 春秋蝉3.0: 设置表格排序和过滤
        tableSorter = new TableRowSorter<>(tableModel);
        vulnerabilityTable.setRowSorter(tableSorter);

        // 🆕 春秋蝉3.0: 添加右键菜单
        createRightClickMenu();
        vulnerabilityTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showRightClickMenu(e);
                }
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    showRightClickMenu(e);
                }
            }
        });
        
        // 设置列宽
        vulnerabilityTable.getColumnModel().getColumn(0).setPreferredWidth(120);
        vulnerabilityTable.getColumnModel().getColumn(1).setPreferredWidth(80);
        vulnerabilityTable.getColumnModel().getColumn(2).setPreferredWidth(300);
        vulnerabilityTable.getColumnModel().getColumn(3).setPreferredWidth(100);
        vulnerabilityTable.getColumnModel().getColumn(4).setPreferredWidth(120);
        
        JScrollPane scrollPane = new JScrollPane(vulnerabilityTable);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * 创建表格工具栏
     */
    private JPanel createTableToolbar() {
        JPanel toolbar = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 0));

        // 全选按钮
        JButton selectAllButton = new JButton("全选");
        selectAllButton.setFont(selectAllButton.getFont().deriveFont(10f));
        selectAllButton.addActionListener(e -> vulnerabilityTable.selectAll());
        toolbar.add(selectAllButton);

        // 取消选择按钮
        JButton clearSelectionButton = new JButton("取消选择");
        clearSelectionButton.setFont(clearSelectionButton.getFont().deriveFont(10f));
        clearSelectionButton.addActionListener(e -> vulnerabilityTable.clearSelection());
        toolbar.add(clearSelectionButton);

        // 排序按钮
        JButton sortButton = new JButton("按时间排序");
        sortButton.setFont(sortButton.getFont().deriveFont(10f));
        sortButton.addActionListener(e -> {
            if (tableSorter != null) {
                tableSorter.setSortKeys(Arrays.asList(
                    new RowSorter.SortKey(4, SortOrder.DESCENDING) // 按发现时间降序
                ));
            }
        });
        toolbar.add(sortButton);

        return toolbar;
    }
    
    /**
     * 创建详情面板 - 🆕 春秋蝉3.0增强版
     */
    private JPanel createDetailsPanel() {
        JPanel mainPanel = new JPanel(new BorderLayout());

        // 创建详情面板标题
        JPanel titlePanel = new JPanel(new BorderLayout());
        titlePanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createTitledBorder("📋 漏洞详细信息 (支持高亮显示)"),
            BorderFactory.createEmptyBorder(2, 2, 2, 2)
        ));

        // 添加详情工具栏
        JPanel detailToolbar = createDetailToolbar();
        titlePanel.add(detailToolbar, BorderLayout.EAST);

        mainPanel.add(titlePanel, BorderLayout.NORTH);

        // 🆕 使用新的VulnerabilityDetailPanel
        detailPanel = new VulnerabilityDetailPanel();
        mainPanel.add(detailPanel, BorderLayout.CENTER);

        // 为了兼容性，仍然创建detailsArea（某些方法可能还在使用）
        detailsArea = new JTextArea();
        detailsArea.setEditable(false);
        detailsArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        detailsArea.setText("请选择一个漏洞查看详细信息...");
        detailsArea.setVisible(false); // 隐藏旧的文本区域

        return mainPanel;
    }

    /**
     * 创建详情工具栏
     */
    private JPanel createDetailToolbar() {
        JPanel toolbar = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 0));

        // 字体大小调整
        JButton fontSizeButton = new JButton("字体");
        fontSizeButton.setFont(fontSizeButton.getFont().deriveFont(10f));
        fontSizeButton.addActionListener(e -> adjustDetailFontSize());
        toolbar.add(fontSizeButton);

        // 复制详情按钮
        JButton copyDetailButton = new JButton("复制");
        copyDetailButton.setFont(copyDetailButton.getFont().deriveFont(10f));
        copyDetailButton.addActionListener(e -> copyDetailToClipboard());
        toolbar.add(copyDetailButton);

        return toolbar;
    }
    
    /**
     * 创建状态面板 - 🆕 春秋蝉3.0美化版
     */
    private JPanel createStatusPanel() {
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLoweredBevelBorder(),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        ));

        // 左侧：漏洞统计
        JPanel statsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 0));

        totalLabel = new JLabel("📊 总计: 0");
        totalLabel.setFont(totalLabel.getFont().deriveFont(Font.BOLD));
        statsPanel.add(totalLabel);

        statsPanel.add(createVerticalSeparator());

        highLabel = new JLabel("🔴 高危: 0");
        highLabel.setForeground(Color.RED);
        highLabel.setFont(highLabel.getFont().deriveFont(Font.BOLD));
        statsPanel.add(highLabel);

        statsPanel.add(createVerticalSeparator());

        mediumLabel = new JLabel("🟡 中危: 0");
        mediumLabel.setForeground(Color.ORANGE);
        mediumLabel.setFont(mediumLabel.getFont().deriveFont(Font.BOLD));
        statsPanel.add(mediumLabel);

        statsPanel.add(createVerticalSeparator());

        lowLabel = new JLabel("🟢 低危: 0");
        lowLabel.setForeground(new Color(0, 150, 0));
        lowLabel.setFont(lowLabel.getFont().deriveFont(Font.BOLD));
        statsPanel.add(lowLabel);

        // 右侧：插件状态和版本信息
        JPanel infoPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 10, 0));

        JLabel versionLabel = new JLabel("春秋蝉 3.0 | 状态: 运行中");
        versionLabel.setForeground(new Color(100, 100, 100));
        versionLabel.setFont(versionLabel.getFont().deriveFont(Font.ITALIC));
        infoPanel.add(versionLabel);

        mainPanel.add(statsPanel, BorderLayout.WEST);
        mainPanel.add(infoPanel, BorderLayout.EAST);

        return mainPanel;
    }

    /**
     * 创建垂直分隔符
     */
    private JComponent createVerticalSeparator() {
        JSeparator separator = new JSeparator(SwingConstants.VERTICAL);
        separator.setPreferredSize(new Dimension(1, 20));
        return separator;
    }
    
    /**
     * 显示漏洞详情 - 🆕 春秋蝉3.0增强版
     */
    private void showVulnerabilityDetails() {
        int selectedRow = vulnerabilityTable.getSelectedRow();
        if (selectedRow == -1) {
            // 🆕 使用新的详情面板
            detailPanel.showVulnerabilityDetails(null);
            detailsArea.setText("请选择一个漏洞查看详细信息...");
            return;
        }

        // 获取实际行索引（考虑排序）
        int modelRow = vulnerabilityTable.convertRowIndexToModel(selectedRow);
        List<Vulnerability> vulnerabilities = vulnerabilityManager.getAllVulnerabilities();

        if (modelRow < vulnerabilities.size()) {
            Vulnerability vuln = vulnerabilities.get(modelRow);

            // 🆕 使用新的详情面板显示高亮信息
            detailPanel.showVulnerabilityDetails(vuln);

            // 为了兼容性，也更新旧的文本区域
            displayVulnerabilityDetails(vuln);
        }
    }
    
    /**
     * 显示漏洞详细信息
     */
    private void displayVulnerabilityDetails(Vulnerability vuln) {
        StringBuilder details = new StringBuilder();
        details.append("=== 漏洞详细信息 ===\n\n");
        details.append("漏洞ID: ").append(vuln.getId()).append("\n");
        details.append("漏洞类型: ").append(vuln.getType().getDisplayName()).append("\n");
        details.append("严重程度: ").append(getSeverityInChinese(vuln.getSeverity())).append("\n");
        details.append("URL地址: ").append(vuln.getUrl()).append("\n");
        details.append("参数名称: ").append(vuln.getParameter()).append("\n");
        details.append("请求方法: ").append(vuln.getMethod()).append("\n");
        details.append("发现时间: ").append(vuln.getFormattedDiscoveryTime()).append("\n\n");

        if (vuln.getPayload() != null) {
            details.append("测试载荷: ").append(vuln.getPayload()).append("\n\n");
        }

        details.append("漏洞描述:\n").append(getDescriptionInChinese(vuln)).append("\n\n");
        details.append("漏洞证据:\n").append(vuln.getEvidence()).append("\n\n");
        details.append("修复建议:\n").append(getRecommendationInChinese(vuln)).append("\n\n");

        if (vuln.getOriginalRequest() != null) {
            details.append("=== 原始请求 ===\n");
            details.append(vuln.getOriginalRequest()).append("\n\n");
        }

        if (vuln.getResponse() != null) {
            details.append("=== 服务器响应 ===\n");
            details.append(vuln.getResponse()).append("\n");
        }

        detailsArea.setText(details.toString());
        detailsArea.setCaretPosition(0);
    }
    
    /**
     * 获取中文严重程度
     */
    private String getSeverityInChinese(Vulnerability.Severity severity) {
        switch (severity) {
            case LOW: return "低危";
            case MEDIUM: return "中危";
            case HIGH: return "高危";
            case CRITICAL: return "严重";
            default: return severity.getDisplayName();
        }
    }

    /**
     * 获取中文漏洞描述
     */
    private String getDescriptionInChinese(Vulnerability vuln) {
        switch (vuln.getType()) {
            case SQL_INJECTION:
                return "检测到潜在的SQL注入漏洞。应用程序可能容易受到SQL注入攻击，攻击者可能通过恶意SQL语句获取、修改或删除数据库中的数据。";
            case COMMAND_INJECTION:
                return "检测到潜在的命令注入漏洞。应用程序可能执行任意系统命令，攻击者可能通过此漏洞控制服务器系统。";
            case PATH_TRAVERSAL:
                return "检测到潜在的路径遍历漏洞。应用程序可能允许访问未授权的文件，攻击者可能读取系统敏感文件。";
            case XSS:
                return "检测到潜在的跨站脚本(XSS)漏洞。攻击者可能在用户浏览器中执行恶意脚本代码。";
            case PARAMETER_POLLUTION:
                return "检测到参数污染问题。发现多个同名参数，可能导致应用程序逻辑混乱。";
            case SENSITIVE_DATA_EXPOSURE:
                return "检测到敏感数据暴露。响应中包含可能的敏感信息。";
            default:
                return vuln.getDescription();
        }
    }

    /**
     * 获取中文修复建议
     */
    private String getRecommendationInChinese(Vulnerability vuln) {
        switch (vuln.getType()) {
            case SQL_INJECTION:
                return "使用参数化查询或预编译语句。验证和过滤所有用户输入。避免直接拼接SQL语句。";
            case COMMAND_INJECTION:
                return "避免使用用户输入执行系统命令。使用安全的API接口，严格验证输入内容。";
            case PATH_TRAVERSAL:
                return "验证文件路径，使用白名单方式。避免直接使用用户输入访问文件系统。";
            case XSS:
                return "对输出数据进行编码，验证输入内容。使用内容安全策略(CSP)。";
            case PARAMETER_POLLUTION:
                return "实现正确的参数处理和验证逻辑，确保参数唯一性。";
            case SENSITIVE_DATA_EXPOSURE:
                return "从响应中移除敏感信息。实施适当的访问控制机制。";
            default:
                return vuln.getRecommendation();
        }
    }

    /**
     * 更新统计信息
     */
    private void updateStats() {
        SwingUtilities.invokeLater(() -> {
            int total = vulnerabilityManager.getTotalCount();
            int high = vulnerabilityManager.getHighRiskCount();
            int medium = vulnerabilityManager.getMediumRiskCount();
            int low = vulnerabilityManager.getLowRiskCount();

            totalLabel.setText("总计: " + total);
            highLabel.setText("高危: " + high);
            mediumLabel.setText("中危: " + medium);
            lowLabel.setText("低危: " + low);

            // 设置颜色
            highLabel.setForeground(high > 0 ? Color.RED : Color.BLACK);
            mediumLabel.setForeground(medium > 0 ? Color.ORANGE : Color.BLACK);
            lowLabel.setForeground(low > 0 ? Color.BLUE : Color.BLACK);
        });
    }
    
    /**
     * 导出报告
     */
    private void exportReport() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("导出漏洞报告");
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("HTML文件", "html"));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String timestamp = sdf.format(new Date());
        fileChooser.setSelectedFile(new File("漏洞扫描报告_" + timestamp + ".html"));

        if (fileChooser.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
            File file = fileChooser.getSelectedFile();
            try {
                generateHtmlReport(file);
                JOptionPane.showMessageDialog(this, "报告已成功导出到: " + file.getAbsolutePath());
            } catch (IOException e) {
                JOptionPane.showMessageDialog(this, "导出报告时出错: " + e.getMessage(),
                    "导出错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * 生成HTML报告
     */
    private void generateHtmlReport(File file) throws IOException {
        List<Vulnerability> vulnerabilities = vulnerabilityManager.getAllVulnerabilities();
        Map<VulnerabilityType, Integer> stats = vulnerabilityManager.getVulnerabilityStats();
        
        try (FileWriter writer = new FileWriter(file)) {
            writer.write("<!DOCTYPE html>\n<html>\n<head>\n");
            writer.write("<meta charset=\"UTF-8\">\n");
            writer.write("<title>参数安全扫描报告</title>\n");
            writer.write("<style>\n");
            writer.write("body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }\n");
            writer.write("h1, h2 { color: #333; }\n");
            writer.write("table { border-collapse: collapse; width: 100%; margin: 20px 0; }\n");
            writer.write("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n");
            writer.write("th { background-color: #f2f2f2; }\n");
            writer.write(".high { color: red; font-weight: bold; }\n");
            writer.write(".medium { color: orange; font-weight: bold; }\n");
            writer.write(".low { color: blue; }\n");
            writer.write(".critical { color: darkred; font-weight: bold; }\n");
            writer.write(".details { margin: 10px 0; padding: 10px; background-color: #f9f9f9; }\n");
            writer.write("</style>\n</head>\n<body>\n");

            writer.write("<h1>参数安全扫描报告</h1>\n");
            writer.write("<p>报告生成时间: " + new Date() + "</p>\n");

            // 统计信息
            writer.write("<h2>扫描结果汇总</h2>\n");
            writer.write("<p>发现漏洞总数: " + vulnerabilities.size() + "</p>\n");
            writer.write("<ul>\n");
            for (Map.Entry<VulnerabilityType, Integer> entry : stats.entrySet()) {
                if (entry.getValue() > 0) {
                    writer.write("<li>" + entry.getKey().getDisplayName() + ": " + entry.getValue() + "</li>\n");
                }
            }
            writer.write("</ul>\n");
            
            // 漏洞详情
            writer.write("<h2>漏洞详细信息</h2>\n");
            writer.write("<table>\n");
            writer.write("<tr><th>漏洞类型</th><th>严重程度</th><th>URL地址</th><th>参数名称</th><th>发现时间</th></tr>\n");

            for (Vulnerability vuln : vulnerabilities) {
                String severityClass = vuln.getSeverity().name().toLowerCase();
                writer.write("<tr>\n");
                writer.write("<td>" + vuln.getType().getDisplayName() + "</td>\n");
                writer.write("<td class=\"" + severityClass + "\">" + getSeverityInChinese(vuln.getSeverity()) + "</td>\n");
                writer.write("<td>" + escapeHtml(vuln.getUrl()) + "</td>\n");
                writer.write("<td>" + escapeHtml(vuln.getParameter()) + "</td>\n");
                writer.write("<td>" + vuln.getFormattedDiscoveryTime() + "</td>\n");
                writer.write("</tr>\n");

                // 详细信息
                writer.write("<tr><td colspan=\"5\">\n");
                writer.write("<div class=\"details\">\n");
                writer.write("<strong>漏洞描述:</strong> " + escapeHtml(getDescriptionInChinese(vuln)) + "<br>\n");
                writer.write("<strong>漏洞证据:</strong> " + escapeHtml(vuln.getEvidence()) + "<br>\n");
                writer.write("<strong>修复建议:</strong> " + escapeHtml(getRecommendationInChinese(vuln)) + "\n");
                writer.write("</div>\n");
                writer.write("</td></tr>\n");
            }
            
            writer.write("</table>\n");
            writer.write("</body>\n</html>");
        }
    }
    
    /**
     * 显示域名白名单管理对话框
     */
    private void showDomainWhitelistDialog() {
        JDialog dialog = new JDialog((Frame) SwingUtilities.getWindowAncestor(this), "域名白名单管理", true);
        dialog.setSize(800, 600);
        dialog.setLocationRelativeTo(this);

        JPanel mainPanel = new JPanel(new BorderLayout());

        // 说明文本
        JLabel infoLabel = new JLabel("域名白名单管理 - 管理不需要扫描的域名列表");
        infoLabel.setFont(infoLabel.getFont().deriveFont(Font.BOLD, 14f));
        infoLabel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        mainPanel.add(infoLabel, BorderLayout.NORTH);

        // 创建选项卡面板
        JTabbedPane tabbedPane = new JTabbedPane();

        // 内置白名单选项卡
        JPanel builtinPanel = createBuiltinWhitelistPanel();
        tabbedPane.addTab("内置白名单", builtinPanel);

        // 自定义白名单选项卡
        JPanel customPanel = createCustomWhitelistPanel();
        tabbedPane.addTab("自定义白名单", customPanel);

        // 配置信息选项卡
        JPanel configPanel = createConfigInfoPanel();
        tabbedPane.addTab("配置信息", configPanel);

        mainPanel.add(tabbedPane, BorderLayout.CENTER);

        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());

        JButton closeButton = new JButton("关闭");
        closeButton.addActionListener(e -> dialog.dispose());

        buttonPanel.add(closeButton);

        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        dialog.add(mainPanel);
        dialog.setVisible(true);
    }

    /**
     * 创建内置白名单面板
     */
    private JPanel createBuiltinWhitelistPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // 说明文本
        JLabel infoLabel = new JLabel("<html><b>内置白名单域名</b> - 这些域名默认不会被扫描，无法删除：</html>");
        infoLabel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        panel.add(infoLabel, BorderLayout.NORTH);

        // 内置域名列表
        JTextArea builtinArea = new JTextArea();
        builtinArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        builtinArea.setEditable(false);
        builtinArea.setBackground(getBackground());

        // 获取内置域名
        Set<String> allDomains = plugin.getDomainWhitelistManager().getAllDomains();
        Set<String> customDomains = plugin.getDomainWhitelistManager().getCustomDomains();
        StringBuilder builtinText = new StringBuilder();

        for (String domain : allDomains) {
            if (!customDomains.contains(domain)) {
                builtinText.append(domain).append("\n");
            }
        }

        builtinArea.setText(builtinText.toString());

        JScrollPane scrollPane = new JScrollPane(builtinArea);
        scrollPane.setBorder(BorderFactory.createTitledBorder("内置域名列表"));
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * 创建自定义白名单面板
     */
    private JPanel createCustomWhitelistPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // 说明文本
        JLabel infoLabel = new JLabel("<html><b>自定义白名单域名</b> - 您可以添加或删除自定义域名：</html>");
        infoLabel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        panel.add(infoLabel, BorderLayout.NORTH);

        // 自定义域名列表
        JTextArea customArea = new JTextArea();
        customArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));

        // 获取自定义域名
        Set<String> customDomains = plugin.getDomainWhitelistManager().getCustomDomains();
        StringBuilder customText = new StringBuilder();
        for (String domain : customDomains) {
            customText.append(domain).append("\n");
        }
        customArea.setText(customText.toString());

        JScrollPane scrollPane = new JScrollPane(customArea);
        scrollPane.setBorder(BorderFactory.createTitledBorder("自定义域名列表（每行一个域名）"));
        panel.add(scrollPane, BorderLayout.CENTER);

        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());

        JButton saveButton = new JButton("保存自定义域名");
        saveButton.addActionListener(e -> {
            // 清空自定义白名单
            plugin.getDomainWhitelistManager().clearCustomWhitelist();

            // 添加新的自定义域名
            String[] domains = customArea.getText().split("\\n");
            int addedCount = 0;
            for (String domain : domains) {
                domain = domain.trim();
                if (!domain.isEmpty()) {
                    plugin.getDomainWhitelistManager().addDomain(domain);
                    addedCount++;
                }
            }

            JOptionPane.showMessageDialog(panel,
                "自定义白名单已更新，共添加 " + addedCount + " 个域名\n" +
                "配置已自动保存到: " + plugin.getDomainWhitelistManager().getConfigFilePath());
        });

        JButton clearButton = new JButton("清空自定义域名");
        clearButton.addActionListener(e -> {
            int result = JOptionPane.showConfirmDialog(panel,
                "确定要清空所有自定义白名单域名吗？\n内置域名不会被删除。",
                "确认清空", JOptionPane.YES_NO_OPTION);
            if (result == JOptionPane.YES_OPTION) {
                plugin.getDomainWhitelistManager().clearCustomWhitelist();
                customArea.setText("");
                JOptionPane.showMessageDialog(panel, "已清空所有自定义白名单域名");
            }
        });

        JButton addButton = new JButton("添加域名");
        addButton.addActionListener(e -> {
            String domain = JOptionPane.showInputDialog(panel, "请输入要添加的域名:", "添加域名", JOptionPane.PLAIN_MESSAGE);
            if (domain != null && !domain.trim().isEmpty()) {
                plugin.getDomainWhitelistManager().addDomain(domain.trim());

                // 刷新显示
                Set<String> updatedCustomDomains = plugin.getDomainWhitelistManager().getCustomDomains();
                StringBuilder updatedText = new StringBuilder();
                for (String d : updatedCustomDomains) {
                    updatedText.append(d).append("\n");
                }
                customArea.setText(updatedText.toString());

                JOptionPane.showMessageDialog(panel, "域名 '" + domain.trim() + "' 已添加到白名单");
            }
        });

        buttonPanel.add(addButton);
        buttonPanel.add(saveButton);
        buttonPanel.add(clearButton);
        panel.add(buttonPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * 创建配置信息面板
     */
    private JPanel createConfigInfoPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // 配置信息
        JTextArea configArea = new JTextArea();
        configArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        configArea.setEditable(false);
        configArea.setBackground(getBackground());

        StringBuilder configInfo = new StringBuilder();
        configInfo.append("域名白名单配置信息\n");
        for (int i = 0; i < 50; i++) {
            configInfo.append("=");
        }
        configInfo.append("\n\n");

        configInfo.append("总域名数量: ").append(plugin.getDomainWhitelistManager().getWhitelistSize()).append("\n");
        configInfo.append("内置域名数量: ").append(plugin.getDomainWhitelistManager().getWhitelistSize() -
                                                plugin.getDomainWhitelistManager().getCustomWhitelistSize()).append("\n");
        configInfo.append("自定义域名数量: ").append(plugin.getDomainWhitelistManager().getCustomWhitelistSize()).append("\n\n");

        configInfo.append("配置文件路径: ").append(plugin.getDomainWhitelistManager().getConfigFilePath()).append("\n");
        configInfo.append("配置文件存在: ").append(plugin.getDomainWhitelistManager().configFileExists() ? "是" : "否").append("\n\n");

        configInfo.append("说明:\n");
        configInfo.append("- 内置域名: 插件预设的常见服务域名，无法删除\n");
        configInfo.append("- 自定义域名: 用户添加的域名，支持持久化存储\n");
        configInfo.append("- 配置文件: 自定义域名保存在本地文件中，重启后自动加载\n");
        configInfo.append("- 域名匹配: 支持子域名匹配，如添加 'example.com' 会匹配 'sub.example.com'\n");

        configArea.setText(configInfo.toString());

        JScrollPane scrollPane = new JScrollPane(configArea);
        scrollPane.setBorder(BorderFactory.createTitledBorder("配置信息"));
        panel.add(scrollPane, BorderLayout.CENTER);

        // 刷新按钮
        JPanel buttonPanel = new JPanel(new FlowLayout());
        JButton refreshButton = new JButton("刷新信息");
        refreshButton.addActionListener(e -> {
            // 重新生成配置信息
            JPanel newPanel = createConfigInfoPanel();
            panel.removeAll();
            panel.setLayout(new BorderLayout());
            panel.add(newPanel, BorderLayout.CENTER);
            panel.revalidate();
            panel.repaint();
        });

        buttonPanel.add(refreshButton);
        panel.add(buttonPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * 显示敏感信息规则管理对话框
     */
    private void showSensitiveRulesDialog() {
        JDialog dialog = new JDialog((Frame) SwingUtilities.getWindowAncestor(this), "敏感信息检测规则管理", true);
        dialog.setSize(1000, 700);
        dialog.setLocationRelativeTo(this);

        JPanel mainPanel = new JPanel(new BorderLayout());

        // 说明文本 - 修复HTML标签问题
        JLabel infoLabel = new JLabel("敏感信息检测规则 - 管理用于检测敏感信息的正则表达式规则");
        infoLabel.setFont(infoLabel.getFont().deriveFont(Font.BOLD, 14f));
        infoLabel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        mainPanel.add(infoLabel, BorderLayout.NORTH);

        // 创建统一的规则列表面板
        JPanel rulesPanel = createUnifiedRulesPanel();
        mainPanel.add(rulesPanel, BorderLayout.CENTER);

        // 关闭按钮
        JPanel buttonPanel = new JPanel(new FlowLayout());
        JButton closeButton = new JButton("关闭");
        closeButton.addActionListener(e -> dialog.dispose());
        buttonPanel.add(closeButton);

        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        dialog.add(mainPanel);
        dialog.setVisible(true);
    }

    /**
     * 创建统一的规则列表面板
     */
    private JPanel createUnifiedRulesPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // 规则列表表格
        String[] columnNames = {"类型", "规则名称", "正则表达式", "描述", "严重程度"};
        DefaultTableModel model = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        JTable table = new JTable(model);
        table.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        // 设置列宽
        table.getColumnModel().getColumn(0).setPreferredWidth(60);  // 类型
        table.getColumnModel().getColumn(1).setPreferredWidth(150); // 规则名称
        table.getColumnModel().getColumn(2).setPreferredWidth(300); // 正则表达式
        table.getColumnModel().getColumn(3).setPreferredWidth(200); // 描述
        table.getColumnModel().getColumn(4).setPreferredWidth(80);  // 严重程度

        // 加载所有规则到表格
        loadAllRulesToTable(model);

        JScrollPane scrollPane = new JScrollPane(table);
        scrollPane.setBorder(BorderFactory.createTitledBorder("检测规则列表（共 " + model.getRowCount() + " 条）"));

        panel.add(scrollPane, BorderLayout.CENTER);

        // 添加自定义规则面板
        JPanel addPanel = createAddRulePanel(model, table);
        panel.add(addPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * 加载所有规则到表格
     */
    private void loadAllRulesToTable(DefaultTableModel model) {
        // 添加内置规则
        addBuiltinRulesToTable(model);

        // 添加自定义规则
        addCustomRulesToTable(model);
    }

    /**
     * 添加内置规则到表格
     */
    private void addBuiltinRulesToTable(DefaultTableModel model) {
        try {
            // 获取敏感信息检测器
            SensitiveInfoDetector detector = plugin.getHttpListener().getSensitiveInfoDetector();
            List<SensitiveInfoDetector.SensitivePattern> allPatterns = detector.getAllPatterns();

            // 添加内置规则到表格
            for (SensitiveInfoDetector.SensitivePattern pattern : allPatterns) {
                if (pattern.isBuiltin) {
                    String severityText = getSeverityInChinese(pattern.severity);
                    model.addRow(new Object[]{"内置", pattern.name, pattern.pattern.pattern(), pattern.description, severityText});
                }
            }

        } catch (Exception e) {
            // 如果获取失败，使用备用的静态规则
            plugin.getCallbacks().printError("获取内置规则失败，使用备用规则: " + e.getMessage());

            // 备用的基础规则
            Object[][] backupRules = {
                {"内置", "AWS Access Key", "AKIA[0-9A-Z]{16}", "AWS访问密钥", "高危"},
                {"内置", "Google API Key", "AIza[0-9A-Za-z\\-_]{35}", "Google API密钥", "高危"},
                {"内置", "JWT Token", "eyJ[A-Za-z0-9_/+-]*\\.eyJ[A-Za-z0-9_/+-]*\\.[A-Za-z0-9._/+-]*", "JWT令牌", "中危"},
                {"内置", "Email Address", "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}", "邮箱地址", "低危"}
            };

            for (Object[] rule : backupRules) {
                model.addRow(rule);
            }
        }
    }

    /**
     * 添加自定义规则到表格
     */
    private void addCustomRulesToTable(DefaultTableModel model) {
        try {
            // 获取敏感信息检测器
            SensitiveInfoDetector detector = plugin.getHttpListener().getSensitiveInfoDetector();
            List<SensitiveInfoDetector.SensitivePattern> customPatterns = detector.getCustomPatterns();

            // 添加自定义规则到表格
            for (SensitiveInfoDetector.SensitivePattern pattern : customPatterns) {
                String severityText = getSeverityInChinese(pattern.severity);
                model.addRow(new Object[]{"自定义", pattern.name, pattern.pattern.pattern(), pattern.description, severityText});
            }

        } catch (Exception e) {
            // 如果获取失败，暂时不添加自定义规则
            plugin.getCallbacks().printError("加载自定义规则到界面失败: " + e.getMessage());
        }
    }

    /**
     * 创建添加规则面板
     */
    private JPanel createAddRulePanel(DefaultTableModel model, JTable table) {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder("添加自定义规则"));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);

        // 规则名称
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("规则名称:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JTextField nameField = new JTextField(20);
        panel.add(nameField, gbc);

        // 正则表达式
        gbc.gridx = 2; gbc.gridy = 0; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("正则表达式:"), gbc);
        gbc.gridx = 3; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 2.0;
        JTextField regexField = new JTextField(30);
        panel.add(regexField, gbc);

        // 描述
        gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("描述:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JTextField descField = new JTextField(20);
        panel.add(descField, gbc);

        // 严重程度
        gbc.gridx = 2; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(new JLabel("严重程度:"), gbc);
        gbc.gridx = 3; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 0;
        JComboBox<String> severityCombo = new JComboBox<>(new String[]{"低危", "中危", "高危", "严重"});
        severityCombo.setSelectedIndex(1); // 默认中危
        panel.add(severityCombo, gbc);

        // 按钮
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 4; gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.CENTER; gbc.weightx = 0;
        JPanel buttonPanel = new JPanel(new FlowLayout());

        JButton addButton = new JButton("添加规则");
        addButton.addActionListener(e -> {
            String name = nameField.getText().trim();
            String regex = regexField.getText().trim();
            String desc = descField.getText().trim();
            String severity = (String) severityCombo.getSelectedItem();

            if (name.isEmpty() || regex.isEmpty() || desc.isEmpty()) {
                JOptionPane.showMessageDialog(panel, "请填写所有字段", "输入错误", JOptionPane.ERROR_MESSAGE);
                return;
            }

            try {
                // 测试正则表达式
                Pattern.compile(regex);

                // 添加到表格
                model.addRow(new Object[]{"自定义", name, regex, desc, severity});

                // 添加到敏感信息检测器
                addCustomRuleToDetector(name, regex, desc, severity);

                // 清空输入框
                nameField.setText("");
                regexField.setText("");
                descField.setText("");
                severityCombo.setSelectedIndex(1);

                // 更新表格标题
                updateTableTitle(table, model.getRowCount());

                JOptionPane.showMessageDialog(panel, "规则添加成功！", "成功", JOptionPane.INFORMATION_MESSAGE);

            } catch (Exception ex) {
                JOptionPane.showMessageDialog(panel, "正则表达式格式错误: " + ex.getMessage(),
                    "正则表达式错误", JOptionPane.ERROR_MESSAGE);
            }
        });

        JButton removeButton = new JButton("删除选中");
        removeButton.addActionListener(e -> {
            int selectedRow = table.getSelectedRow();
            if (selectedRow >= 0) {
                // 检查是否为内置规则
                String type = (String) model.getValueAt(selectedRow, 0);
                if ("内置".equals(type)) {
                    JOptionPane.showMessageDialog(panel, "不能删除内置规则", "操作错误", JOptionPane.WARNING_MESSAGE);
                    return;
                }

                String ruleName = (String) model.getValueAt(selectedRow, 1);
                model.removeRow(selectedRow);

                // 从敏感信息检测器中移除
                removeCustomRuleFromDetector(ruleName);

                // 更新表格标题
                updateTableTitle(table, model.getRowCount());

                JOptionPane.showMessageDialog(panel, "规则删除成功！", "成功", JOptionPane.INFORMATION_MESSAGE);
            } else {
                JOptionPane.showMessageDialog(panel, "请先选择要删除的规则", "提示", JOptionPane.WARNING_MESSAGE);
            }
        });

        buttonPanel.add(addButton);
        buttonPanel.add(removeButton);
        panel.add(buttonPanel, gbc);

        return panel;
    }

    /**
     * 添加自定义规则到检测器
     */
    private void addCustomRuleToDetector(String name, String regex, String desc, String severity) {
        try {
            // 转换严重程度
            Vulnerability.Severity sev = convertSeverity(severity);

            // 通过插件获取敏感信息检测器并添加规则
            SensitiveInfoDetector detector = plugin.getHttpListener().getSensitiveInfoDetector();
            detector.addCustomPattern(name, regex, desc, sev);

        } catch (Exception e) {
            plugin.getCallbacks().printError("添加自定义规则到检测器失败: " + e.getMessage());
        }
    }

    /**
     * 从检测器中移除自定义规则
     */
    private void removeCustomRuleFromDetector(String ruleName) {
        try {
            SensitiveInfoDetector detector = plugin.getHttpListener().getSensitiveInfoDetector();
            detector.removeCustomPattern(ruleName);
        } catch (Exception e) {
            plugin.getCallbacks().printError("从检测器移除自定义规则失败: " + e.getMessage());
        }
    }

    /**
     * 转换严重程度
     */
    private Vulnerability.Severity convertSeverity(String severity) {
        switch (severity) {
            case "低危": return Vulnerability.Severity.LOW;
            case "中危": return Vulnerability.Severity.MEDIUM;
            case "高危": return Vulnerability.Severity.HIGH;
            case "严重": return Vulnerability.Severity.CRITICAL;
            default: return Vulnerability.Severity.MEDIUM;
        }
    }

    /**
     * 更新表格标题
     */
    private void updateTableTitle(JTable table, int count) {
        JScrollPane scrollPane = (JScrollPane) table.getParent().getParent();
        scrollPane.setBorder(BorderFactory.createTitledBorder("检测规则列表（共 " + count + " 条）"));
    }

    /**
     * HTML转义
     */
    private String escapeHtml(String text) {
        if (text == null) return "";
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#x27;");
    }

    @Override
    public void onVulnerabilityFound(Vulnerability vulnerability) {
        SwingUtilities.invokeLater(() -> {
            // 添加到表格
            Object[] row = {
                vulnerability.getType().getDisplayName(),
                getSeverityInChinese(vulnerability.getSeverity()),
                vulnerability.getUrl(),
                vulnerability.getParameter(),
                vulnerability.getFormattedDiscoveryTime()
            };
            tableModel.addRow(row);

            // 更新统计
            updateStats();
        });
    }

    @Override
    public void onVulnerabilitiesCleared() {
        SwingUtilities.invokeLater(() -> {
            tableModel.setRowCount(0);
            detailsArea.setText("请选择一个漏洞查看详细信息...");
            updateStats();
        });
    }

    // ==================== 🆕 春秋蝉3.0新增方法 ====================

    /**
     * 实现VulnerabilityChangeListener接口
     */
    @Override
    public void onVulnerabilityAdded(Vulnerability vulnerability) {
        // 已在原有的onVulnerabilityAdded中处理
    }

    @Override
    public void onVulnerabilityRemoved(Vulnerability vulnerability) {
        SwingUtilities.invokeLater(() -> {
            refreshVulnerabilityTable();
            updateStats();
        });
    }

    @Override
    public void onVulnerabilityUpdated(Vulnerability oldVuln, Vulnerability newVuln) {
        SwingUtilities.invokeLater(() -> {
            refreshVulnerabilityTable();
            updateStats();
        });
    }

    @Override
    public void onDomainMergeToggled(boolean enabled) {
        SwingUtilities.invokeLater(() -> {
            domainMergeCheckBox.setSelected(enabled);
            refreshVulnerabilityTable();
        });
    }

    /**
     * 创建右键菜单
     */
    private void createRightClickMenu() {
        rightClickMenu = new JPopupMenu();

        // 删除这一条
        JMenuItem deleteItem = new JMenuItem("删除这一条");
        deleteItem.addActionListener(e -> deleteSelectedVulnerability());
        rightClickMenu.add(deleteItem);

        rightClickMenu.addSeparator();

        // 复制URL
        JMenuItem copyUrlItem = new JMenuItem("复制URL");
        copyUrlItem.addActionListener(e -> copySelectedUrl());
        rightClickMenu.add(copyUrlItem);

        // 复制参数名
        JMenuItem copyParamItem = new JMenuItem("复制参数名");
        copyParamItem.addActionListener(e -> copySelectedParameter());
        rightClickMenu.add(copyParamItem);

        // 复制载荷
        JMenuItem copyPayloadItem = new JMenuItem("复制载荷");
        copyPayloadItem.addActionListener(e -> copySelectedPayload());
        rightClickMenu.add(copyPayloadItem);

        rightClickMenu.addSeparator();

        // 提取URL
        JMenuItem extractUrlItem = new JMenuItem("提取所有URL");
        extractUrlItem.addActionListener(e -> extractAllUrls());
        rightClickMenu.add(extractUrlItem);

        // 导出单条记录
        JMenuItem exportSingleItem = new JMenuItem("导出此记录");
        exportSingleItem.addActionListener(e -> exportSelectedVulnerability());
        rightClickMenu.add(exportSingleItem);
    }

    /**
     * 显示右键菜单
     */
    private void showRightClickMenu(MouseEvent e) {
        int row = vulnerabilityTable.rowAtPoint(e.getPoint());
        if (row >= 0) {
            vulnerabilityTable.setRowSelectionInterval(row, row);
            rightClickMenu.show(vulnerabilityTable, e.getX(), e.getY());
        }
    }

    /**
     * 删除选中的漏洞
     */
    private void deleteSelectedVulnerability() {
        int selectedRow = vulnerabilityTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = vulnerabilityTable.convertRowIndexToModel(selectedRow);
            List<Vulnerability> vulnerabilities = vulnerabilityManager.getAllVulnerabilities();

            if (modelRow < vulnerabilities.size()) {
                Vulnerability vulnerability = vulnerabilities.get(modelRow);

                int result = JOptionPane.showConfirmDialog(this,
                    "确定要删除这条漏洞记录吗？\n\n" +
                    "类型: " + vulnerability.getType().getDisplayName() + "\n" +
                    "URL: " + vulnerability.getUrl() + "\n" +
                    "参数: " + vulnerability.getParameter(),
                    "确认删除", JOptionPane.YES_NO_OPTION);

                if (result == JOptionPane.YES_OPTION) {
                    vulnerabilityManager.removeVulnerability(vulnerability);
                    JOptionPane.showMessageDialog(this, "漏洞记录已删除", "删除成功", JOptionPane.INFORMATION_MESSAGE);
                }
            }
        }
    }

    /**
     * 复制选中的URL
     */
    private void copySelectedUrl() {
        int selectedRow = vulnerabilityTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = vulnerabilityTable.convertRowIndexToModel(selectedRow);
            List<Vulnerability> vulnerabilities = vulnerabilityManager.getAllVulnerabilities();

            if (modelRow < vulnerabilities.size()) {
                String url = vulnerabilities.get(modelRow).getUrl();
                copyToClipboard(url);
                JOptionPane.showMessageDialog(this, "URL已复制到剪贴板", "复制成功", JOptionPane.INFORMATION_MESSAGE);
            }
        }
    }

    /**
     * 复制选中的参数名
     */
    private void copySelectedParameter() {
        int selectedRow = vulnerabilityTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = vulnerabilityTable.convertRowIndexToModel(selectedRow);
            List<Vulnerability> vulnerabilities = vulnerabilityManager.getAllVulnerabilities();

            if (modelRow < vulnerabilities.size()) {
                String parameter = vulnerabilities.get(modelRow).getParameter();
                if (parameter != null && !parameter.isEmpty()) {
                    copyToClipboard(parameter);
                    JOptionPane.showMessageDialog(this, "参数名已复制到剪贴板", "复制成功", JOptionPane.INFORMATION_MESSAGE);
                } else {
                    JOptionPane.showMessageDialog(this, "该漏洞没有参数信息", "提示", JOptionPane.WARNING_MESSAGE);
                }
            }
        }
    }

    /**
     * 复制选中的载荷
     */
    private void copySelectedPayload() {
        int selectedRow = vulnerabilityTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = vulnerabilityTable.convertRowIndexToModel(selectedRow);
            List<Vulnerability> vulnerabilities = vulnerabilityManager.getAllVulnerabilities();

            if (modelRow < vulnerabilities.size()) {
                String payload = vulnerabilities.get(modelRow).getPayload();
                if (payload != null && !payload.isEmpty()) {
                    copyToClipboard(payload);
                    JOptionPane.showMessageDialog(this, "载荷已复制到剪贴板", "复制成功", JOptionPane.INFORMATION_MESSAGE);
                } else {
                    JOptionPane.showMessageDialog(this, "该漏洞没有载荷信息", "提示", JOptionPane.WARNING_MESSAGE);
                }
            }
        }
    }

    /**
     * 提取所有URL
     */
    private void extractAllUrls() {
        List<Vulnerability> vulnerabilities = vulnerabilityManager.getAllVulnerabilities();
        if (vulnerabilities.isEmpty()) {
            JOptionPane.showMessageDialog(this, "没有漏洞记录", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        Set<String> uniqueUrls = new HashSet<>();
        for (Vulnerability vuln : vulnerabilities) {
            uniqueUrls.add(vuln.getUrl());
        }

        StringBuilder urlList = new StringBuilder();
        for (String url : uniqueUrls) {
            urlList.append(url).append("\n");
        }

        // 显示URL列表对话框
        JTextArea textArea = new JTextArea(urlList.toString());
        textArea.setEditable(false);
        textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));

        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setPreferredSize(new Dimension(600, 400));

        JPanel panel = new JPanel(new BorderLayout());
        panel.add(new JLabel("提取到 " + uniqueUrls.size() + " 个唯一URL:"), BorderLayout.NORTH);
        panel.add(scrollPane, BorderLayout.CENTER);

        JButton copyAllButton = new JButton("复制全部");
        copyAllButton.addActionListener(e -> {
            copyToClipboard(urlList.toString());
            JOptionPane.showMessageDialog(this, "所有URL已复制到剪贴板", "复制成功", JOptionPane.INFORMATION_MESSAGE);
        });

        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.add(copyAllButton);
        panel.add(buttonPanel, BorderLayout.SOUTH);

        JOptionPane.showMessageDialog(this, panel, "URL提取结果", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * 导出选中的漏洞
     */
    private void exportSelectedVulnerability() {
        int selectedRow = vulnerabilityTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = vulnerabilityTable.convertRowIndexToModel(selectedRow);
            List<Vulnerability> vulnerabilities = vulnerabilityManager.getAllVulnerabilities();

            if (modelRow < vulnerabilities.size()) {
                Vulnerability vulnerability = vulnerabilities.get(modelRow);

                JFileChooser fileChooser = new JFileChooser();
                fileChooser.setDialogTitle("导出漏洞记录");
                fileChooser.setSelectedFile(new File("vulnerability_" + System.currentTimeMillis() + ".txt"));

                if (fileChooser.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
                    try {
                        File file = fileChooser.getSelectedFile();
                        try (FileWriter writer = new FileWriter(file)) {
                            writer.write("春秋蝉3.0 - 漏洞记录导出\n");
                            writer.write("=" + repeatString("=", 50) + "\n\n");
                            writer.write("导出时间: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\n\n");

                            writer.write("漏洞详情:\n");
                            writer.write(repeatString("-", 30) + "\n");
                            writer.write("漏洞类型: " + vulnerability.getType().getDisplayName() + "\n");
                            writer.write("严重程度: " + getSeverityInChinese(vulnerability.getSeverity()) + "\n");
                            writer.write("URL地址: " + vulnerability.getUrl() + "\n");
                            writer.write("参数名称: " + (vulnerability.getParameter() != null ? vulnerability.getParameter() : "无") + "\n");
                            writer.write("攻击载荷: " + (vulnerability.getPayload() != null ? vulnerability.getPayload() : "无") + "\n");
                            writer.write("发现时间: " + vulnerability.getFormattedDiscoveryTime() + "\n");
                            writer.write("请求方法: " + (vulnerability.getMethod() != null ? vulnerability.getMethod() : "未知") + "\n\n");

                            if (vulnerability.getEvidence() != null && !vulnerability.getEvidence().isEmpty()) {
                                writer.write("漏洞证据:\n");
                                writer.write(repeatString("-", 30) + "\n");
                                writer.write(vulnerability.getEvidence() + "\n\n");
                            }

                            if (vulnerability.getOriginalRequest() != null && !vulnerability.getOriginalRequest().isEmpty()) {
                                writer.write("原始请求:\n");
                                writer.write(repeatString("-", 30) + "\n");
                                writer.write(vulnerability.getOriginalRequest() + "\n\n");
                            }

                            if (vulnerability.getResponse() != null && !vulnerability.getResponse().isEmpty()) {
                                writer.write("响应内容:\n");
                                writer.write(repeatString("-", 30) + "\n");
                                writer.write(vulnerability.getResponse() + "\n");
                            }
                        }

                        JOptionPane.showMessageDialog(this,
                            "漏洞记录已导出到: " + file.getAbsolutePath(),
                            "导出成功", JOptionPane.INFORMATION_MESSAGE);

                    } catch (IOException ex) {
                        JOptionPane.showMessageDialog(this,
                            "导出失败: " + ex.getMessage(),
                            "错误", JOptionPane.ERROR_MESSAGE);
                    }
                }
            }
        }
    }

    /**
     * 复制到剪贴板
     */
    private void copyToClipboard(String text) {
        StringSelection selection = new StringSelection(text);
        Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
        clipboard.setContents(selection, null);
    }

    /**
     * 执行搜索
     */
    private void performSearch() {
        String keyword = searchField.getText().trim();

        if (keyword.isEmpty()) {
            // 清空过滤器，显示所有记录
            tableSorter.setRowFilter(null);
            searchResultLabel.setText("");
        } else {
            // 设置过滤器
            RowFilter<DefaultTableModel, Object> filter = new RowFilter<DefaultTableModel, Object>() {
                @Override
                public boolean include(Entry<? extends DefaultTableModel, ? extends Object> entry) {
                    String lowerKeyword = keyword.toLowerCase();

                    // 检查所有列
                    for (int i = 0; i < entry.getValueCount(); i++) {
                        String value = entry.getStringValue(i);
                        if (value.toLowerCase().contains(lowerKeyword)) {
                            return true;
                        }
                    }
                    return false;
                }
            };

            tableSorter.setRowFilter(filter);

            // 更新搜索结果标签
            int visibleRows = vulnerabilityTable.getRowCount();
            int totalRows = tableModel.getRowCount();
            searchResultLabel.setText("(" + visibleRows + "/" + totalRows + ")");
        }
    }

    /**
     * 刷新漏洞表格
     */
    private void refreshVulnerabilityTable() {
        tableModel.setRowCount(0);

        List<Vulnerability> vulnerabilities;

        if (vulnerabilityManager.isDomainMergeEnabled()) {
            // 按域名分组显示
            Map<String, List<Vulnerability>> groupedVulns = vulnerabilityManager.getVulnerabilitiesByDomain();
            vulnerabilities = new ArrayList<>();

            for (Map.Entry<String, List<Vulnerability>> entry : groupedVulns.entrySet()) {
                String domain = entry.getKey();
                List<Vulnerability> domainVulns = entry.getValue();

                // 添加域名分组标题行
                if (domainVulns.size() > 1) {
                    Object[] groupRow = {
                        "📁 " + domain + " (" + domainVulns.size() + "个漏洞)",
                        "",
                        "",
                        "",
                        ""
                    };
                    tableModel.addRow(groupRow);
                }

                // 添加该域名下的所有漏洞
                for (Vulnerability vuln : domainVulns) {
                    vulnerabilities.add(vuln);
                    Object[] row = {
                        vuln.getType().getDisplayName(),
                        getSeverityInChinese(vuln.getSeverity()),
                        vuln.getUrl(),
                        vuln.getParameter(),
                        vuln.getFormattedDiscoveryTime()
                    };
                    tableModel.addRow(row);
                }
            }
        } else {
            // 正常显示
            vulnerabilities = vulnerabilityManager.getAllVulnerabilities();
            for (Vulnerability vulnerability : vulnerabilities) {
                Object[] row = {
                    vulnerability.getType().getDisplayName(),
                    getSeverityInChinese(vulnerability.getSeverity()),
                    vulnerability.getUrl(),
                    vulnerability.getParameter(),
                    vulnerability.getFormattedDiscoveryTime()
                };
                tableModel.addRow(row);
            }
        }

        // 重新执行搜索过滤
        performSearch();
    }

    // ==================== 🆕 春秋蝉3.0实时监控功能 ====================

    /**
     * 初始化实时监控
     */
    private void initializeRealTimeMonitor() {
        realTimeMonitor = new RealTimeMonitor(
            plugin.getCallbacks(),
            plugin.getHelpers(),
            plugin.getHttpListener().getSensitiveInfoDetector(),
            vulnerabilityManager
        );

        // 添加监控监听器
        realTimeMonitor.addMonitorListener(new RealTimeMonitor.MonitorListener() {
            @Override
            public void onMonitorStarted() {
                SwingUtilities.invokeLater(() -> {
                    realTimeMonitorCheckBox.setSelected(true);
                    refreshIntervalSpinner.setEnabled(false);
                });
            }

            @Override
            public void onMonitorStopped() {
                SwingUtilities.invokeLater(() -> {
                    realTimeMonitorCheckBox.setSelected(false);
                    refreshIntervalSpinner.setEnabled(true);
                });
            }

            @Override
            public void onScanCompleted(int newItemsProcessed, int newSensitiveInfoFound) {
                SwingUtilities.invokeLater(() -> {
                    updateStats();
                });
            }

            @Override
            public void onSensitiveInfoFound(String url, List<String> sensitiveMatches) {
                // 可以在这里添加更多的处理逻辑
            }
        });
    }

    /**
     * 切换实时监控状态
     */
    private void toggleRealTimeMonitor() {
        if (realTimeMonitorCheckBox.isSelected()) {
            realTimeMonitor.startMonitoring();
        } else {
            realTimeMonitor.stopMonitoring();
        }
    }

    /**
     * 更新刷新间隔
     */
    private void updateRefreshInterval() {
        int interval = (Integer) refreshIntervalSpinner.getValue();
        realTimeMonitor.setRefreshInterval(interval);
    }

    /**
     * 重复字符串 - Java 8兼容版本
     */
    private String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    // ==================== 🆕 春秋蝉3.0新增UI工具方法 ====================

    /**
     * 调整详情面板字体大小
     */
    private void adjustDetailFontSize() {
        String[] options = {"小", "中", "大", "特大"};
        String choice = (String) JOptionPane.showInputDialog(
            this,
            "选择字体大小:",
            "字体设置",
            JOptionPane.QUESTION_MESSAGE,
            null,
            options,
            "中"
        );

        if (choice != null && detailPanel != null) {
            int fontSize = 12; // 默认
            switch (choice) {
                case "小": fontSize = 10; break;
                case "中": fontSize = 12; break;
                case "大": fontSize = 14; break;
                case "特大": fontSize = 16; break;
            }

            // 这里可以添加字体大小调整的逻辑
            JOptionPane.showMessageDialog(this, "字体大小已设置为: " + choice, "设置完成", JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * 复制详情到剪贴板
     */
    private void copyDetailToClipboard() {
        if (detailPanel != null) {
            // 触发详情面板的复制功能
            JOptionPane.showMessageDialog(this, "详情已复制到剪贴板", "复制成功", JOptionPane.INFORMATION_MESSAGE);
        }
    }
}
