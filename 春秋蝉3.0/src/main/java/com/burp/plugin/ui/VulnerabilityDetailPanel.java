package com.burp.plugin.ui;

import com.burp.plugin.model.Vulnerability;

import javax.swing.*;
import javax.swing.text.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 漏洞详情面板 - 春秋蝉3.0增强版
 * 支持关键信息高亮显示、复制功能、格式化显示等
 */
public class VulnerabilityDetailPanel extends JPanel {
    
    private JTextPane detailPane;
    private StyledDocument document;
    private Vulnerability currentVulnerability;
    
    // 高亮样式
    private Style urlStyle;
    private Style payloadStyle;
    private Style parameterStyle;
    private Style severityStyle;
    private Style evidenceStyle;
    private Style normalStyle;
    private Style headerStyle;
    
    public VulnerabilityDetailPanel() {
        initializeUI();
        setupStyles();
    }
    
    /**
     * 初始化UI
     */
    private void initializeUI() {
        setLayout(new BorderLayout());
        
        // 创建文本面板
        detailPane = new JTextPane();
        detailPane.setEditable(false);
        detailPane.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        detailPane.setBackground(Color.WHITE);
        
        document = detailPane.getStyledDocument();
        
        // 添加滚动面板
        JScrollPane scrollPane = new JScrollPane(detailPane);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setBorder(BorderFactory.createTitledBorder("漏洞详情"));
        
        add(scrollPane, BorderLayout.CENTER);
        
        // 创建操作按钮面板
        JPanel buttonPanel = createButtonPanel();
        add(buttonPanel, BorderLayout.SOUTH);
        
        // 显示默认信息
        showDefaultMessage();
    }
    
    /**
     * 设置文本样式
     */
    private void setupStyles() {
        // 普通样式
        normalStyle = document.addStyle("normal", null);
        StyleConstants.setFontFamily(normalStyle, Font.MONOSPACED);
        StyleConstants.setFontSize(normalStyle, 12);
        StyleConstants.setForeground(normalStyle, Color.BLACK);
        
        // 标题样式
        headerStyle = document.addStyle("header", normalStyle);
        StyleConstants.setBold(headerStyle, true);
        StyleConstants.setFontSize(headerStyle, 14);
        StyleConstants.setForeground(headerStyle, new Color(0, 102, 204));
        
        // URL样式 - 蓝色
        urlStyle = document.addStyle("url", normalStyle);
        StyleConstants.setForeground(urlStyle, new Color(0, 0, 255));
        StyleConstants.setBold(urlStyle, true);
        StyleConstants.setUnderline(urlStyle, true);
        
        // 载荷样式 - 红色
        payloadStyle = document.addStyle("payload", normalStyle);
        StyleConstants.setForeground(payloadStyle, new Color(255, 0, 0));
        StyleConstants.setBold(payloadStyle, true);
        StyleConstants.setBackground(payloadStyle, new Color(255, 255, 200));
        
        // 参数样式 - 绿色
        parameterStyle = document.addStyle("parameter", normalStyle);
        StyleConstants.setForeground(parameterStyle, new Color(0, 128, 0));
        StyleConstants.setBold(parameterStyle, true);
        
        // 严重程度样式 - 根据级别变色
        severityStyle = document.addStyle("severity", normalStyle);
        StyleConstants.setBold(severityStyle, true);
        
        // 证据样式 - 橙色背景
        evidenceStyle = document.addStyle("evidence", normalStyle);
        StyleConstants.setBackground(evidenceStyle, new Color(255, 248, 220));
        StyleConstants.setForeground(evidenceStyle, new Color(139, 69, 19));
    }
    
    /**
     * 创建按钮面板
     */
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        
        // 复制全部按钮
        JButton copyAllButton = new JButton("复制全部");
        copyAllButton.addActionListener(e -> copyAllText());
        panel.add(copyAllButton);
        
        // 复制URL按钮
        JButton copyUrlButton = new JButton("复制URL");
        copyUrlButton.addActionListener(e -> copyUrl());
        panel.add(copyUrlButton);
        
        // 复制载荷按钮
        JButton copyPayloadButton = new JButton("复制载荷");
        copyPayloadButton.addActionListener(e -> copyPayload());
        panel.add(copyPayloadButton);
        
        // 刷新按钮
        JButton refreshButton = new JButton("刷新");
        refreshButton.addActionListener(e -> refreshDisplay());
        panel.add(refreshButton);
        
        return panel;
    }
    
    /**
     * 显示漏洞详情
     */
    public void showVulnerabilityDetails(Vulnerability vulnerability) {
        this.currentVulnerability = vulnerability;
        
        if (vulnerability == null) {
            showDefaultMessage();
            return;
        }
        
        try {
            // 清空文档
            document.remove(0, document.getLength());
            
            // 添加标题
            insertText("🔍 漏洞详细信息\n", headerStyle);
            insertText("=" + repeatString("=", 60) + "\n\n", normalStyle);
            
            // 基本信息
            insertText("📋 基本信息\n", headerStyle);
            insertText(repeatString("-", 30) + "\n", normalStyle);
            
            insertText("漏洞类型: ", normalStyle);
            insertText(vulnerability.getType().getDisplayName(), payloadStyle);
            insertText("\n", normalStyle);
            
            insertText("严重程度: ", normalStyle);
            insertSeverity(vulnerability.getSeverity());
            insertText("\n", normalStyle);
            
            insertText("发现时间: ", normalStyle);
            insertText(vulnerability.getFormattedDiscoveryTime(), normalStyle);
            insertText("\n", normalStyle);
            
            insertText("请求方法: ", normalStyle);
            insertText(vulnerability.getMethod() != null ? vulnerability.getMethod() : "未知", normalStyle);
            insertText("\n\n", normalStyle);
            
            // URL信息
            insertText("🌐 URL信息\n", headerStyle);
            insertText(repeatString("-", 30) + "\n", normalStyle);
            insertText("目标URL: ", normalStyle);
            insertText(vulnerability.getUrl(), urlStyle);
            insertText("\n\n", normalStyle);

            // 参数信息
            if (vulnerability.getParameter() != null && !vulnerability.getParameter().isEmpty()) {
                insertText("📝 参数信息\n", headerStyle);
                insertText(repeatString("-", 30) + "\n", normalStyle);
                insertText("参数名称: ", normalStyle);
                insertText(vulnerability.getParameter(), parameterStyle);
                insertText("\n\n", normalStyle);
            }
            
            // 载荷信息
            if (vulnerability.getPayload() != null && !vulnerability.getPayload().isEmpty()) {
                insertText("💣 攻击载荷\n", headerStyle);
                insertText(repeatString("-", 30) + "\n", normalStyle);
                insertText("载荷内容: ", normalStyle);
                insertText(vulnerability.getPayload(), payloadStyle);
                insertText("\n\n", normalStyle);
            }
            
            // 漏洞证据
            if (vulnerability.getEvidence() != null && !vulnerability.getEvidence().isEmpty()) {
                insertText("🔍 漏洞证据\n", headerStyle);
                insertText(repeatString("-", 30) + "\n", normalStyle);
                insertHighlightedEvidence(vulnerability.getEvidence());
                insertText("\n\n", normalStyle);
            }
            
            // 原始请求
            if (vulnerability.getOriginalRequest() != null && !vulnerability.getOriginalRequest().isEmpty()) {
                insertText("📤 原始请求\n", headerStyle);
                insertText(repeatString("-", 30) + "\n", normalStyle);
                insertHighlightedRequest(vulnerability.getOriginalRequest(), vulnerability.getParameter(), vulnerability.getPayload());
                insertText("\n\n", normalStyle);
            }
            
            // 响应内容
            if (vulnerability.getResponse() != null && !vulnerability.getResponse().isEmpty()) {
                insertText("📥 响应内容\n", headerStyle);
                insertText(repeatString("-", 30) + "\n", normalStyle);
                
                String response = vulnerability.getResponse();
                if (response.length() > 2000) {
                    insertText("(响应内容过长，仅显示前2000字符)\n\n", normalStyle);
                    response = response.substring(0, 2000) + "...";
                }
                
                insertHighlightedResponse(response, vulnerability.getPayload());
                insertText("\n", normalStyle);
            }
            
            // 滚动到顶部
            detailPane.setCaretPosition(0);
            
        } catch (BadLocationException e) {
            showErrorMessage("显示漏洞详情时出错: " + e.getMessage());
        }
    }
    
    /**
     * 插入文本
     */
    private void insertText(String text, Style style) throws BadLocationException {
        document.insertString(document.getLength(), text, style);
    }
    
    /**
     * 插入严重程度（带颜色）
     */
    private void insertSeverity(Vulnerability.Severity severity) throws BadLocationException {
        Style style = document.addStyle("tempSeverity", severityStyle);
        
        switch (severity) {
            case HIGH:
                StyleConstants.setForeground(style, Color.RED);
                insertText("高危", style);
                break;
            case MEDIUM:
                StyleConstants.setForeground(style, Color.ORANGE);
                insertText("中危", style);
                break;
            case LOW:
                StyleConstants.setForeground(style, new Color(255, 165, 0));
                insertText("低危", style);
                break;
            case CRITICAL:
                StyleConstants.setForeground(style, new Color(139, 0, 0));
                insertText("严重", style);
                break;
        }
    }
    
    /**
     * 插入高亮的证据
     */
    private void insertHighlightedEvidence(String evidence) throws BadLocationException {
        insertText(evidence, evidenceStyle);
    }
    
    /**
     * 插入高亮的请求
     */
    private void insertHighlightedRequest(String request, String parameter, String payload) throws BadLocationException {
        if (parameter == null && payload == null) {
            insertText(request, normalStyle);
            return;
        }
        
        String text = request;
        int lastIndex = 0;
        
        // 高亮参数名
        if (parameter != null) {
            Pattern paramPattern = Pattern.compile("\\b" + Pattern.quote(parameter) + "\\b", Pattern.CASE_INSENSITIVE);
            Matcher matcher = paramPattern.matcher(text);
            
            while (matcher.find()) {
                // 插入前面的普通文本
                if (matcher.start() > lastIndex) {
                    insertText(text.substring(lastIndex, matcher.start()), normalStyle);
                }
                // 插入高亮的参数名
                insertText(matcher.group(), parameterStyle);
                lastIndex = matcher.end();
            }
        }
        
        // 插入剩余的文本
        if (lastIndex < text.length()) {
            String remaining = text.substring(lastIndex);
            
            // 高亮载荷
            if (payload != null && remaining.contains(payload)) {
                highlightPayloadInText(remaining, payload);
            } else {
                insertText(remaining, normalStyle);
            }
        }
    }
    
    /**
     * 插入高亮的响应
     */
    private void insertHighlightedResponse(String response, String payload) throws BadLocationException {
        if (payload == null) {
            insertText(response, normalStyle);
            return;
        }
        
        highlightPayloadInText(response, payload);
    }
    
    /**
     * 在文本中高亮载荷
     */
    private void highlightPayloadInText(String text, String payload) throws BadLocationException {
        int index = text.toLowerCase().indexOf(payload.toLowerCase());
        
        if (index == -1) {
            insertText(text, normalStyle);
            return;
        }
        
        // 插入载荷前的文本
        if (index > 0) {
            insertText(text.substring(0, index), normalStyle);
        }
        
        // 插入高亮的载荷
        insertText(text.substring(index, index + payload.length()), payloadStyle);
        
        // 递归处理剩余文本
        if (index + payload.length() < text.length()) {
            highlightPayloadInText(text.substring(index + payload.length()), payload);
        }
    }
    
    /**
     * 显示默认消息
     */
    private void showDefaultMessage() {
        try {
            document.remove(0, document.getLength());
            insertText("请选择一个漏洞查看详细信息...\n\n", normalStyle);
            insertText("💡 提示:\n", headerStyle);
            insertText("• 在漏洞列表中点击任意漏洞查看详情\n", normalStyle);
            insertText("• 关键信息将以不同颜色高亮显示\n", normalStyle);
            insertText("• 可以使用下方按钮复制相关信息\n", normalStyle);
        } catch (BadLocationException e) {
            // 忽略错误
        }
    }
    
    /**
     * 显示错误消息
     */
    private void showErrorMessage(String message) {
        try {
            document.remove(0, document.getLength());
            insertText("❌ 错误: " + message, normalStyle);
        } catch (BadLocationException e) {
            // 忽略错误
        }
    }
    
    /**
     * 复制全部文本
     */
    private void copyAllText() {
        if (currentVulnerability != null) {
            String text = detailPane.getText();
            copyToClipboard(text);
            JOptionPane.showMessageDialog(this, "漏洞详情已复制到剪贴板", "复制成功", JOptionPane.INFORMATION_MESSAGE);
        }
    }
    
    /**
     * 复制URL
     */
    private void copyUrl() {
        if (currentVulnerability != null) {
            copyToClipboard(currentVulnerability.getUrl());
            JOptionPane.showMessageDialog(this, "URL已复制到剪贴板", "复制成功", JOptionPane.INFORMATION_MESSAGE);
        }
    }
    
    /**
     * 复制载荷
     */
    private void copyPayload() {
        if (currentVulnerability != null && currentVulnerability.getPayload() != null) {
            copyToClipboard(currentVulnerability.getPayload());
            JOptionPane.showMessageDialog(this, "载荷已复制到剪贴板", "复制成功", JOptionPane.INFORMATION_MESSAGE);
        } else {
            JOptionPane.showMessageDialog(this, "该漏洞没有载荷信息", "提示", JOptionPane.WARNING_MESSAGE);
        }
    }
    
    /**
     * 刷新显示
     */
    private void refreshDisplay() {
        if (currentVulnerability != null) {
            showVulnerabilityDetails(currentVulnerability);
        }
    }
    
    /**
     * 复制到剪贴板
     */
    private void copyToClipboard(String text) {
        java.awt.datatransfer.StringSelection selection = new java.awt.datatransfer.StringSelection(text);
        java.awt.datatransfer.Clipboard clipboard = java.awt.Toolkit.getDefaultToolkit().getSystemClipboard();
        clipboard.setContents(selection, null);
    }

    /**
     * 重复字符串 - Java 8兼容版本
     */
    private String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
}
