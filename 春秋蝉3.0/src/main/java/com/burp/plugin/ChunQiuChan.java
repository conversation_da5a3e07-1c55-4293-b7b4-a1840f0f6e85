package com.burp.plugin;

import burp.*;
import com.burp.plugin.ui.MainPanel;
import com.burp.plugin.core.HttpListener;
import com.burp.plugin.core.VulnerabilityManager;
import com.burp.plugin.core.CrawlerManager;
import com.burp.plugin.core.DomainWhitelistManager;
import com.burp.plugin.core.ActiveScanManager;

import javax.swing.*;
import java.awt.*;
import java.io.PrintWriter;

/**
 * 春秋蝉 (ChunQiu Chan) - Burp Suite 专业参数安全检测扩展
 *
 * 功能包括：
 * - 参数分析和提取
 * - SQL注入检测 (15+种模式)
 * - API模糊测试
 * - 敏感信息检测 (21个内置规则 + 自定义规则持久化)
 * - 同域爬虫
 * - 域名白名单 (25+个预设域名)
 * - 漏洞管理和报告
 *
 * <AUTHOR> Team
 * @version 3.0.0
 */
public class ChunQiuChan implements IBurpExtender, ITab, IHttpListener {
    
    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private PrintWriter stdout;
    private PrintWriter stderr;
    
    // UI组件
    private MainPanel mainPanel;
    
    // 核心功能模块
    private HttpListener httpListener;
    private VulnerabilityManager vulnerabilityManager;
    private CrawlerManager crawlerManager;
    private DomainWhitelistManager domainWhitelistManager;
    private ActiveScanManager activeScanManager;
    
    // 插件配置
    private boolean isEnabled = true;
    private boolean sqlInjectionTestEnabled = true;
    private boolean xssTestEnabled = true; // 🆕 春秋蝉3.0: XSS检测开关
    private boolean apiFuzzingEnabled = true;
    private boolean crawlerEnabled = false; // 默认关闭爬虫
    private boolean sensitiveInfoDetectionEnabled = true; // 默认启用敏感信息检测
    
    @Override
    public void registerExtenderCallbacks(IBurpExtenderCallbacks callbacks) {
        this.callbacks = callbacks;
        this.helpers = callbacks.getHelpers();
        this.stdout = new PrintWriter(callbacks.getStdout(), true);
        this.stderr = new PrintWriter(callbacks.getStderr(), true);
        
        // 设置插件名称
        callbacks.setExtensionName("春秋蝉 3.0 (ChunQiu Chan 3.0)");
        
        // 初始化核心模块
        initializeModules();
        
        // 注册HTTP监听器
        callbacks.registerHttpListener(this);

        // 创建UI界面并添加标签页
        initializeUI();
        
        stdout.println("春秋蝉 3.0 (ChunQiu Chan 3.0) 插件加载成功！");
        stdout.println("功能包括: 增强SQL注入检测、主动扫描、爬虫集成检测、敏感信息检测、同域爬虫");
    }
    
    /**
     * 初始化核心功能模块
     */
    private void initializeModules() {
        vulnerabilityManager = new VulnerabilityManager();
        domainWhitelistManager = new DomainWhitelistManager();
        crawlerManager = new CrawlerManager(callbacks, helpers, vulnerabilityManager);

        // 🚀 创建主动扫描管理器
        activeScanManager = new ActiveScanManager(callbacks, helpers, vulnerabilityManager, domainWhitelistManager);

        // 🔗 将爬虫与主动扫描管理器集成
        crawlerManager.setActiveScanManager(activeScanManager);

        httpListener = new HttpListener(callbacks, helpers, vulnerabilityManager, crawlerManager, domainWhitelistManager);

        stdout.println("域名白名单已加载，包含 " + domainWhitelistManager.getWhitelistSize() + " 个域名");
        stdout.println("敏感信息检测器已加载，包含 " + httpListener.getSensitiveInfoDetector().getAllPatterns().size() + " 个检测规则");
        stdout.println("🎯 主动扫描管理器已启动 - 爬虫发现的URL将自动进行安全测试");
    }
    
    /**
     * 初始化UI界面
     */
    private void initializeUI() {
        try {
            if (SwingUtilities.isEventDispatchThread()) {
                // 如果已经在EDT线程中，直接创建
                createUI();
            } else {
                // 否则在EDT线程中创建
                SwingUtilities.invokeAndWait(() -> createUI());
            }

            // 添加插件标签页
            callbacks.addSuiteTab(this);

        } catch (Exception e) {
            stderr.println("Error initializing UI: " + e.getMessage());
            e.printStackTrace(stderr);
        }
    }

    /**
     * 创建UI组件
     */
    private void createUI() {
        mainPanel = new MainPanel(this, vulnerabilityManager);
    }
    
    @Override
    public String getTabCaption() {
        return "春秋蝉 3.0";
    }
    
    @Override
    public Component getUiComponent() {
        if (mainPanel == null) {
            // 如果UI还没有初始化，创建一个临时面板
            JPanel tempPanel = new JPanel();
            tempPanel.add(new JLabel("Loading Parameter Security Scanner..."));
            return tempPanel;
        }
        return mainPanel;
    }
    
    @Override
    public void processHttpMessage(int toolFlag, boolean messageIsRequest, IHttpRequestResponse messageInfo) {
        // 只处理Proxy工具的消息
        if (toolFlag == IBurpExtenderCallbacks.TOOL_PROXY && !messageIsRequest && isEnabled) {
            httpListener.processHttpMessage(toolFlag, messageIsRequest, messageInfo);
        }
    }
    
    // Getter和Setter方法
    public boolean isEnabled() {
        return isEnabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.isEnabled = enabled;
        stdout.println("插件已" + (enabled ? "启用" : "禁用"));
    }

    public boolean isSqlInjectionTestEnabled() {
        return sqlInjectionTestEnabled;
    }

    public void setSqlInjectionTestEnabled(boolean enabled) {
        this.sqlInjectionTestEnabled = enabled;
        httpListener.setSqlInjectionTestEnabled(enabled);
        stdout.println("SQL注入检测已" + (enabled ? "启用" : "禁用"));
    }

    // 🆕 春秋蝉3.0: XSS检测开关方法的实现
    public void setXssTestEnabled(boolean enabled) {
        this.xssTestEnabled = enabled;
        httpListener.setXssTestEnabled(enabled);
        stdout.println("XSS检测已" + (enabled ? "启用" : "禁用"));
    }

    public boolean isApiFuzzingEnabled() {
        return apiFuzzingEnabled;
    }

    public void setApiFuzzingEnabled(boolean enabled) {
        this.apiFuzzingEnabled = enabled;
        httpListener.setApiFuzzingEnabled(enabled);
        stdout.println("API模糊测试已" + (enabled ? "启用" : "禁用"));
    }

    public boolean isCrawlerEnabled() {
        return crawlerEnabled;
    }

    public void setCrawlerEnabled(boolean enabled) {
        this.crawlerEnabled = enabled;
        httpListener.setCrawlerEnabled(enabled);
        stdout.println("同域爬虫已" + (enabled ? "启用" : "禁用"));
    }

    public boolean isSensitiveInfoDetectionEnabled() {
        return sensitiveInfoDetectionEnabled;
    }

    public void setSensitiveInfoDetectionEnabled(boolean enabled) {
        this.sensitiveInfoDetectionEnabled = enabled;
        httpListener.setSensitiveInfoDetectionEnabled(enabled);
        stdout.println("敏感信息检测已" + (enabled ? "启用" : "禁用"));
    }
    
    public IBurpExtenderCallbacks getCallbacks() {
        return callbacks;
    }
    
    public IExtensionHelpers getHelpers() {
        return helpers;
    }
    
    public PrintWriter getStdout() {
        return stdout;
    }
    
    public PrintWriter getStderr() {
        return stderr;
    }
    
    public VulnerabilityManager getVulnerabilityManager() {
        return vulnerabilityManager;
    }
    
    public CrawlerManager getCrawlerManager() {
        return crawlerManager;
    }

    public DomainWhitelistManager getDomainWhitelistManager() {
        return domainWhitelistManager;
    }

    public HttpListener getHttpListener() {
        return httpListener;
    }

    public ActiveScanManager getActiveScanManager() {
        return activeScanManager;
    }

    // 🆕 春秋蝉3.0: XSS检测开关getter方法
    public boolean isXssTestEnabled() {
        return xssTestEnabled;
    }

}
