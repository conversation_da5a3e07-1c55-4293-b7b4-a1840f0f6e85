package com.burp.plugin.model;

/**
 * 漏洞类型枚举
 */
public enum VulnerabilityType {

    SQL_INJECTION("SQL注入", "SQLi"),
    COMMAND_INJECTION("命令注入", "CMDi"),
    PATH_TRAVERSAL("路径遍历", "PT"),
    XSS("跨站脚本", "XSS"),
    PARAMETER_POLLUTION("参数污染", "PP"),
    SENSITIVE_DATA_EXPOSURE("敏感数据暴露", "SDE");
    
    private final String displayName;
    private final String shortName;
    
    VulnerabilityType(String displayName, String shortName) {
        this.displayName = displayName;
        this.shortName = shortName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getShortName() {
        return shortName;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
