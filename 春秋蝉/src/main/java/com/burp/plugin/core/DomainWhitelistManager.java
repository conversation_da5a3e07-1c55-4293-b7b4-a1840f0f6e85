package com.burp.plugin.core;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 域名白名单管理器 - 管理不需要扫描的域名列表
 */
public class DomainWhitelistManager {
    
    private final Set<String> whitelistedDomains;
    
    public DomainWhitelistManager() {
        this.whitelistedDomains = ConcurrentHashMap.newKeySet();
        initializeDefaultWhitelist();
    }
    
    /**
     * 初始化默认白名单
     */
    private void initializeDefaultWhitelist() {
        // 搜索引擎和相关服务
        addDomain("google.com");
        addDomain("google.cn");
        addDomain("googleapis.com");
        addDomain("googleusercontent.com");
        addDomain("gstatic.com");
        addDomain("gvt1.com");
        addDomain("gvt2.com");
        addDomain("gvt3.com");
        addDomain("bing.com");
        addDomain("baidu.com");
        addDomain("yahoo.com");
        addDomain("yandex.com");
        addDomain("duckduckgo.com");
        
        // 代码托管平台
        addDomain("github.com");
        addDomain("gitlab.com");
        addDomain("bitbucket.org");
        addDomain("gitee.com");
        addDomain("coding.net");
        
        // 社交媒体
        addDomain("facebook.com");
        addDomain("twitter.com");
        addDomain("linkedin.com");
        addDomain("instagram.com");
        addDomain("weibo.com");
        addDomain("qq.com");
        addDomain("wechat.com");
        
        // 云服务商
        addDomain("amazonaws.com");
        addDomain("azure.com");
        addDomain("aliyun.com");
        addDomain("qcloud.com");
        addDomain("cloudflare.com");
        
        // 大型网站
        addDomain("youtube.com");
        addDomain("netflix.com");
        addDomain("amazon.com");
        addDomain("taobao.com");
        addDomain("tmall.com");
        addDomain("jd.com");
        addDomain("wikipedia.org");
        
        // CDN和静态资源
        addDomain("jsdelivr.net");
        addDomain("unpkg.com");
        addDomain("cdnjs.cloudflare.com");
        addDomain("ajax.googleapis.com");
        addDomain("fonts.googleapis.com");
        addDomain("fonts.gstatic.com");
        addDomain("bootstrapcdn.com");
        addDomain("jquery.com");
        addDomain("maxcdn.bootstrapcdn.com");
        addDomain("stackpath.bootstrapcdn.com");
        
        // 广告和分析
        addDomain("doubleclick.net");
        addDomain("googleadservices.com");
        addDomain("googlesyndication.com");
        addDomain("google-analytics.com");
        addDomain("googletagmanager.com");
        addDomain("facebook.net");
        
        // 浏览器和系统更新服务
        addDomain("edgedl.me.gvt1.com");
        addDomain("dl.google.com");
        addDomain("update.googleapis.com");
        addDomain("clients2.google.com");
        addDomain("clients4.google.com");
        addDomain("clients6.google.com");
        addDomain("update.microsoft.com");
        addDomain("windowsupdate.microsoft.com");
        addDomain("download.mozilla.org");

        // Google服务的各种子域名模式
        addDomain("gvt1.com");  // 这会匹配 *.gvt1.com 包括 edgedl.me.gvt1.com
        addDomain("gvt2.com");
        addDomain("gvt3.com");
        addDomain("ggpht.com");
        addDomain("googlehosted.com");

        // 其他知名服务
        addDomain("stackoverflow.com");
        addDomain("reddit.com");
        addDomain("medium.com");
        addDomain("zhihu.com");
        addDomain("csdn.net");
        addDomain("jianshu.com");
    }
    
    /**
     * 添加域名到白名单
     */
    public void addDomain(String domain) {
        if (domain != null && !domain.trim().isEmpty()) {
            whitelistedDomains.add(domain.toLowerCase().trim());
        }
    }
    
    /**
     * 从白名单移除域名
     */
    public void removeDomain(String domain) {
        if (domain != null) {
            whitelistedDomains.remove(domain.toLowerCase().trim());
        }
    }
    
    /**
     * 检查域名是否在白名单中
     */
    public boolean isWhitelisted(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }

        String lowerDomain = domain.toLowerCase().trim();

        // 直接匹配
        if (whitelistedDomains.contains(lowerDomain)) {
            return true;
        }

        // 检查子域名匹配
        for (String whitelistedDomain : whitelistedDomains) {
            if (lowerDomain.endsWith("." + whitelistedDomain)) {
                return true;
            }
        }

        // 检查复杂域名模式（如 edgedl.me.gvt1.com）
        for (String whitelistedDomain : whitelistedDomains) {
            if (isComplexDomainMatch(lowerDomain, whitelistedDomain)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查复杂域名匹配（处理如 edgedl.me.gvt1.com 这样的域名）
     */
    private boolean isComplexDomainMatch(String domain, String whitelistedDomain) {
        // 特殊处理Google的gvt域名
        if (whitelistedDomain.equals("gvt1.com") || whitelistedDomain.equals("gvt2.com") || whitelistedDomain.equals("gvt3.com")) {
            if (domain.contains("gvt1.com") || domain.contains("gvt2.com") || domain.contains("gvt3.com")) {
                return true;
            }
        }

        // 特殊处理googleapis.com相关域名
        if (whitelistedDomain.equals("googleapis.com")) {
            if (domain.contains("googleapis.com")) {
                return true;
            }
        }

        // 检查是否包含白名单域名的关键部分
        String[] domainParts = domain.split("\\.");
        String[] whitelistParts = whitelistedDomain.split("\\.");

        // 如果域名包含白名单域名的后缀部分
        if (domainParts.length >= whitelistParts.length) {
            boolean match = true;
            for (int i = 0; i < whitelistParts.length; i++) {
                int domainIndex = domainParts.length - whitelistParts.length + i;
                if (!domainParts[domainIndex].equals(whitelistParts[i])) {
                    match = false;
                    break;
                }
            }
            if (match) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * 检查URL是否在白名单中
     */
    public boolean isUrlWhitelisted(String url) {
        try {
            java.net.URL urlObj = new java.net.URL(url);
            return isWhitelisted(urlObj.getHost());
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取所有白名单域名
     */
    public Set<String> getAllWhitelistedDomains() {
        return new java.util.HashSet<>(whitelistedDomains);
    }
    
    /**
     * 获取白名单域名数量
     */
    public int getWhitelistSize() {
        return whitelistedDomains.size();
    }
    
    /**
     * 清空白名单
     */
    public void clearWhitelist() {
        whitelistedDomains.clear();
    }
    
    /**
     * 重置为默认白名单
     */
    public void resetToDefault() {
        clearWhitelist();
        initializeDefaultWhitelist();
    }
    
    /**
     * 批量添加域名
     */
    public void addDomains(String[] domains) {
        if (domains != null) {
            for (String domain : domains) {
                addDomain(domain);
            }
        }
    }
    
    /**
     * 从字符串添加域名（逗号分隔）
     */
    public void addDomainsFromString(String domainsString) {
        if (domainsString != null && !domainsString.trim().isEmpty()) {
            String[] domains = domainsString.split("[,;\\s]+");
            addDomains(domains);
        }
    }
    
    /**
     * 导出白名单为字符串
     */
    public String exportWhitelistAsString() {
        return String.join(", ", whitelistedDomains);
    }
}
