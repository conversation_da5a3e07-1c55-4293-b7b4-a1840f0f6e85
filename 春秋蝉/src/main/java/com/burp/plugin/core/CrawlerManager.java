package com.burp.plugin.core;

import burp.*;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.net.URL;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Pattern;

/**
 * 爬虫管理器 - 实现受限制的同域爬虫功能
 */
public class CrawlerManager {
    
    private final IBurpExtenderCallbacks callbacks;
    private final IExtensionHelpers helpers;
    private final VulnerabilityManager vulnerabilityManager;
    
    // 爬虫配置 - 优化性能
    private int maxDepth = 2; // 最大爬取深度
    private int maxUrls = 30; // 减少最大爬取URL数量
    private long crawlDelay = 500; // 减少爬取延迟（毫秒）
    
    // 爬虫状态
    private final Set<String> visitedUrls = ConcurrentHashMap.newKeySet();
    private final Set<String> allowedDomains = ConcurrentHashMap.newKeySet();
    private final Queue<CrawlTask> crawlQueue = new LinkedList<>();
    private final ExecutorService crawlerExecutor = Executors.newSingleThreadExecutor();
    
    // URL过滤模式
    private final List<Pattern> excludePatterns;
    
    public CrawlerManager(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers, 
                         VulnerabilityManager vulnerabilityManager) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        this.vulnerabilityManager = vulnerabilityManager;
        this.excludePatterns = initializeExcludePatterns();
    }
    
    /**
     * 初始化排除模式
     */
    private List<Pattern> initializeExcludePatterns() {
        String[] patterns = {
            ".*\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|pdf|zip|rar|exe|dmg)$",
            ".*logout.*",
            ".*signout.*",
            ".*delete.*",
            ".*remove.*",
            ".*admin.*",
            ".*management.*"
        };
        
        List<Pattern> compiledPatterns = new ArrayList<>();
        for (String pattern : patterns) {
            compiledPatterns.add(Pattern.compile(pattern, Pattern.CASE_INSENSITIVE));
        }
        return compiledPatterns;
    }
    
    /**
     * 从HTTP响应中爬取链接
     */
    public void crawlFromResponse(IHttpRequestResponse messageInfo) {
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(messageInfo);
            URL currentUrl = requestInfo.getUrl();
            
            // 添加当前域名到允许列表
            String domain = currentUrl.getHost();
            allowedDomains.add(domain);
            
            // 解析响应中的链接
            String response = helpers.bytesToString(messageInfo.getResponse());
            IResponseInfo responseInfo = helpers.analyzeResponse(messageInfo.getResponse());
            
            // 只处理HTML响应
            String mimeType = responseInfo.getStatedMimeType();
            if (mimeType != null && !mimeType.toLowerCase().contains("html")) {
                return;
            }
            
            // 提取链接
            Set<String> extractedUrls = extractUrlsFromHtml(response, currentUrl);
            
            // 添加到爬取队列
            for (String url : extractedUrls) {
                if (shouldCrawlUrl(url)) {
                    addToCrawlQueue(url, 1); // 深度为1
                }
            }
            
            // 启动爬虫（如果还没有运行）
            startCrawling();
            
        } catch (Exception e) {
            callbacks.printError("Error in crawler: " + e.getMessage());
        }
    }
    
    /**
     * 从HTML中提取URL
     */
    private Set<String> extractUrlsFromHtml(String html, URL baseUrl) {
        Set<String> urls = new HashSet<>();
        
        try {
            Document doc = Jsoup.parse(html, baseUrl.toString());
            
            // 提取链接
            Elements links = doc.select("a[href]");
            for (Element link : links) {
                String href = link.attr("abs:href");
                if (!href.isEmpty()) {
                    urls.add(href);
                }
            }
            
            // 提取表单action
            Elements forms = doc.select("form[action]");
            for (Element form : forms) {
                String action = form.attr("abs:action");
                if (!action.isEmpty()) {
                    urls.add(action);
                }
            }
            
            // 提取iframe src
            Elements iframes = doc.select("iframe[src]");
            for (Element iframe : iframes) {
                String src = iframe.attr("abs:src");
                if (!src.isEmpty()) {
                    urls.add(src);
                }
            }
            
        } catch (Exception e) {
            callbacks.printError("Error parsing HTML: " + e.getMessage());
        }
        
        return urls;
    }
    
    /**
     * 判断是否应该爬取URL
     */
    private boolean shouldCrawlUrl(String url) {
        try {
            URL urlObj = new URL(url);
            
            // 检查域名是否在允许列表中
            if (!allowedDomains.contains(urlObj.getHost())) {
                return false;
            }
            
            // 检查是否已访问
            if (visitedUrls.contains(url)) {
                return false;
            }
            
            // 检查URL数量限制
            if (visitedUrls.size() >= maxUrls) {
                return false;
            }
            
            // 检查排除模式
            for (Pattern pattern : excludePatterns) {
                if (pattern.matcher(url).matches()) {
                    return false;
                }
            }
            
            return true;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 添加到爬取队列
     */
    private void addToCrawlQueue(String url, int depth) {
        if (depth <= maxDepth) {
            synchronized (crawlQueue) {
                crawlQueue.offer(new CrawlTask(url, depth));
            }
        }
    }
    
    /**
     * 启动爬虫
     */
    private void startCrawling() {
        crawlerExecutor.submit(this::crawlWorker);
    }
    
    /**
     * 爬虫工作线程
     */
    private void crawlWorker() {
        while (!crawlQueue.isEmpty() && visitedUrls.size() < maxUrls) {
            CrawlTask task;
            synchronized (crawlQueue) {
                task = crawlQueue.poll();
            }
            
            if (task == null) {
                break;
            }
            
            try {
                crawlUrl(task.url, task.depth);
                Thread.sleep(crawlDelay); // 延迟避免过于频繁的请求
            } catch (Exception e) {
                callbacks.printError("Error crawling URL " + task.url + ": " + e.getMessage());
            }
        }
    }
    
    /**
     * 爬取单个URL
     */
    private void crawlUrl(String url, int depth) {
        try {
            // 标记为已访问
            visitedUrls.add(url);
            
            // 创建HTTP请求
            URL urlObj = new URL(url);
            IHttpService httpService = helpers.buildHttpService(
                urlObj.getHost(), 
                urlObj.getPort() == -1 ? (urlObj.getProtocol().equals("https") ? 443 : 80) : urlObj.getPort(),
                urlObj.getProtocol().equals("https")
            );
            
            byte[] request = helpers.buildHttpRequest(urlObj);
            
            // 发送请求
            IHttpRequestResponse response = callbacks.makeHttpRequest(httpService, request);
            
            if (response.getResponse() != null) {
                callbacks.printOutput("Crawled: " + url + " (depth: " + depth + ")");
                
                // 如果深度未达到最大值，继续提取链接
                if (depth < maxDepth) {
                    String responseString = helpers.bytesToString(response.getResponse());
                    IResponseInfo responseInfo = helpers.analyzeResponse(response.getResponse());
                    
                    String mimeType = responseInfo.getStatedMimeType();
                    if (mimeType != null && mimeType.toLowerCase().contains("html")) {
                        Set<String> newUrls = extractUrlsFromHtml(responseString, urlObj);
                        
                        for (String newUrl : newUrls) {
                            if (shouldCrawlUrl(newUrl)) {
                                addToCrawlQueue(newUrl, depth + 1);
                            }
                        }
                    }
                }
                
                // 将爬取到的响应也进行安全分析
                // 这里可以调用HttpListener的分析方法，但要避免循环调用
                analyzeDiscoveredUrl(response);
            }
            
        } catch (Exception e) {
            callbacks.printError("Error crawling URL " + url + ": " + e.getMessage());
        }
    }
    
    /**
     * 分析发现的URL
     */
    private void analyzeDiscoveredUrl(IHttpRequestResponse response) {
        // 这里可以对爬取到的页面进行基本的安全检查
        // 例如检查敏感信息暴露等
        try {
            String responseString = helpers.bytesToString(response.getResponse());
            IRequestInfo requestInfo = helpers.analyzeRequest(response);
            String url = requestInfo.getUrl().toString();
            
            // 检查敏感信息暴露
            checkSensitiveDataInCrawledPage(url, responseString);
            
        } catch (Exception e) {
            callbacks.printError("Error analyzing crawled URL: " + e.getMessage());
        }
    }
    
    /**
     * 检查爬取页面中的敏感信息
     */
    private void checkSensitiveDataInCrawledPage(String url, String response) {
        String[] sensitivePatterns = {
            "password\\s*[=:]\\s*['\"]?\\w+['\"]?",
            "api[_-]?key\\s*[=:]\\s*['\"]?[\\w-]+['\"]?",
            "secret\\s*[=:]\\s*['\"]?\\w+['\"]?",
            "token\\s*[=:]\\s*['\"]?[\\w.-]+['\"]?",
            "\\b\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}\\b",
            "\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b"
        };
        
        for (String pattern : sensitivePatterns) {
            if (response.matches("(?i).*" + pattern + ".*")) {
                // 发现敏感信息，但不重复报告
                callbacks.printOutput("Sensitive data found in crawled page: " + url);
                break;
            }
        }
    }
    
    /**
     * 获取爬虫统计信息
     */
    public Map<String, Object> getCrawlerStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("visitedUrls", visitedUrls.size());
        stats.put("queueSize", crawlQueue.size());
        stats.put("allowedDomains", allowedDomains.size());
        stats.put("maxDepth", maxDepth);
        stats.put("maxUrls", maxUrls);
        return stats;
    }
    
    /**
     * 清除爬虫状态
     */
    public void clearCrawlerState() {
        visitedUrls.clear();
        crawlQueue.clear();
        allowedDomains.clear();
    }
    
    /**
     * 设置爬虫配置
     */
    public void setMaxDepth(int maxDepth) {
        this.maxDepth = Math.max(1, Math.min(5, maxDepth)); // 限制在1-5之间
    }
    
    public void setMaxUrls(int maxUrls) {
        this.maxUrls = Math.max(10, Math.min(200, maxUrls)); // 限制在10-200之间
    }
    
    public void setCrawlDelay(long delay) {
        this.crawlDelay = Math.max(100, Math.min(5000, delay)); // 限制在100ms-5s之间
    }
    
    /**
     * 关闭爬虫
     */
    public void shutdown() {
        if (crawlerExecutor != null && !crawlerExecutor.isShutdown()) {
            crawlerExecutor.shutdown();
        }
    }
    
    /**
     * 爬取任务类
     */
    private static class CrawlTask {
        final String url;
        final int depth;
        
        CrawlTask(String url, int depth) {
            this.url = url;
            this.depth = depth;
        }
    }
}
